{"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "eslint.ignoreUntitled": true, "eslint.format.enable": true, "eslint.run": "onSave", "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "tailwindCSS.classAttributes": ["class", "className", "theme"], "tailwindCSS.experimental.classRegex": [["twMerge\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"], ["createTheme\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"]], "typescript.tsdk": "node_modules/typescript/lib", "cSpell.words": ["AIRTABLE", "Albuterol", "Amlodipine", "apexcharts", "Atorvas<PERSON><PERSON>", "autocapture", "clincove", "codeinjection", "Ctmr", "Ctmrs", "<PERSON><PERSON><PERSON>", "Datepicker", "<PERSON><PERSON>", "docex", "ebinder", "ebinders", "Ectmrs", "esource", "flowbite", "Hydrochlorothiazide", "Ka<PERSON><PERSON>", "Levothyroxine", "Lisinopril", "Meds", "msword", "nextjs", "nuqs", "officedocument", "Omeprazole", "openxmlformats", "pageleave", "<PERSON><PERSON><PERSON><PERSON>", "Pipeda", "posthog", "Previewable", "remeda", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sortablejs", "spreadsheetml", "subartifact", "svgmap", "timescape", "TMFISF", "<PERSON><PERSON><PERSON>", "typecheck", "Unassigning", "uncategorize", "uncategorized", "Uppy", "wordprocessingml", "xyflow", "zustand"], "cSpell.ignorePaths": ["package-lock.json", "node_modules", "vscode-extension", ".git/objects", ".vscode", ".vscode-insiders", "messages"], "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "i18n-ally.localesPaths": ["messages"]}