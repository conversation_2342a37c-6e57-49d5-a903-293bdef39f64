---
description: General project guidelines and best practices
type: Always
---

# General Project Guidelines

## Code Structure

- Keep files small and focused on a single responsibility
- Maximum file size should be 250 lines
- Use TypeScript for all new code
- Follow consistent naming conventions

## Naming Conventions

- **Files & Directories**: kebab-case for files and directories
- **Components**: PascalCase for component files and names
- **Hooks**: camelCase prefixed with `use` (e.g., `useFeature`)
- **API Functions**: camelCase descriptive of the action (e.g., `fetchUsers`)
- **Store Names**: camelCase prefixed with `use` and suffixed with `Store` (e.g., `useUserStore`)

## TypeScript Usage

- Define proper interfaces and types for all components, functions, and data structures
- Use proper type narrowing when necessary
- Don't use `any` type unless absolutely necessary
- Export types and interfaces that are used across multiple files

## Import Order

1. External libraries (alphabetical)
2. Internal shared libraries/components
3. Application-specific components
4. Hooks
5. Utils/helpers
6. Types
7. Assets/styles

## Function Structure

- Use arrow functions for component definitions
- Declare hook calls at the top of components
- Extract complex logic into custom hooks
- Keep component functions focused on UI composition

## Comments and Documentation

- Write self-documenting code instead of comments whenever possible
- Use JSDoc for public APIs and complex functions
- Comment complex business logic that can't be simplified

## Package Management

- Use pnpm for package management
- Update dependencies regularly
- Maintain a clean dependency tree

## Environment Variables

- Use `.env` files for environment-specific configuration
- Document all environment variables
- Provide default values for optional variables
- Use Next.js environment variable conventions

## Language and Framework
- Use TypeScript with strict type checking
- Use Next.js for both client and admin portals
- Follow React best practices and functional component patterns

## Code Style
- Keep all files under 250 lines
- Apply uniform code style across the project
- Keep the codebase clean and eliminate redundant code
- Do not generate comments for obvious code; only comment non-trivial parts
- Use descriptive names (e.g., `isLoading`, `hasError`)

## Project Structure
- Follow the Nx monorepo structure
- Respect the separation between apps and packages
- Place shared components in the shared-ui package
- Keep related files grouped together (components, hooks, types, styles)

## Imports and Exports
- Use absolute imports with @ alias
- Group and sort imports using simple-import-sort
- Prefer named exports over default exports
- Use barrel exports (index.ts) for component directories

## Documentation
- Write comprehensive JSDoc documentation for non-trivial functions
- Include type definitions for all parameters and return values
- Document complex business logic with clear explanations
