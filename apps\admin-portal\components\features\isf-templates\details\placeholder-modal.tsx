import { use<PERSON>arams } from "next/navigation";
import { parseAsString, useQueryState } from "nuqs";
import { z } from "zod";

import { <PERSON><PERSON>, CloseButton } from "@/components/ui/button";
import { Form, InputField, Textarea } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { File } from "@/lib/apis/isf-templates";

import {
  useCreateFile,
  useUpdateFile,
} from "./hooks/use-isf-template-detail-mutations";

const schema = z.object({
  title: z
    .string({
      invalid_type_error: "File name is required",
      required_error: "File name is required",
    })
    .min(1, { message: "File name is required" }),
  description: z.string().optional(),
});

type Props = {
  isOpen: boolean;
  onClose: () => void;
  selectedFile: File | null;
};

export const PlaceholderModal = function ({
  isOpen,
  onClose,
  selectedFile,
}: Props) {
  const [parentDirectoryName] = useQueryState(
    "folderName",
    parseAsString.withDefault(""),
  );
  const id = useParams().id as string;
  const isEditing = !!selectedFile;
  const { mutateAsync: updateFile, isPending: isUpdating } = useUpdateFile(id);
  const { mutateAsync: createFile, isPending: isCreating } = useCreateFile(id);

  const onSubmit = async (data: z.infer<typeof schema>) => {
    isEditing
      ? await updateFile({
          ...data,
          parentDirectoryName,
          titleToUpdate: data.title,
          title: selectedFile?.title,
        })
      : await createFile({
          ...data,
          parentDirectoryName,
        });
    onClose();
  };

  return (
    <WrapperModal
      isOpen={isOpen}
      onClose={onClose}
      title={`${selectedFile ? "Update" : "Create"} Placeholder`}
    >
      <Form
        mode="onChange"
        schema={schema}
        onSubmit={onSubmit}
        defaultValues={{
          title: selectedFile?.title || "",
          description: selectedFile?.description || "",
        }}
        className="space-y-4"
      >
        <div className="flex flex-col gap-2">
          <Label htmlFor="title">File Name</Label>
          <InputField
            id="title"
            name="title"
            placeholder="Enter file name..."
          />
        </div>
        <div className="flex flex-col gap-2">
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            name="description"
            placeholder="Enter description..."
          />
        </div>

        <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
          <CloseButton onClose={onClose} />
          <Button
            type="submit"
            color="blue"
            disabled={isCreating || isUpdating}
            isLoading={isCreating || isUpdating}
          >
            Save
          </Button>
        </div>
      </Form>
    </WrapperModal>
  );
};
