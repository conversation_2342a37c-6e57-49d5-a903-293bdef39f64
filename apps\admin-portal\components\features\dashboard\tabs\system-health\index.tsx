"use client";

import { DatabaseGrowth } from "./database-growth";
import { HmacVerificationFailures } from "./hmac-verification-failures";
import { OfflineScanners } from "./offline-scanners";
import { SystemProcessingErrors } from "./system-processing-errors";

type Props = {
  onNavigateToDevices: () => void;
};

export const SystemVitalsTab = ({ onNavigateToDevices }: Props) => {
  return (
    <div className="space-y-8">
      <h1 className="text-xl font-bold text-gray-900 sm:text-2xl dark:text-white">
        System Vitals & Health
      </h1>

      <div className="grid grid-cols-1 gap-8 xl:grid-cols-3">
        <div className="xl:col-span-2">
          <SystemProcessingErrors />
        </div>
        <div className="xl:col-span-1">
          <HmacVerificationFailures />
        </div>
      </div>
      <OfflineScanners onNavigateToDevices={onNavigateToDevices} />
      <DatabaseGrowth />
    </div>
  );
};
