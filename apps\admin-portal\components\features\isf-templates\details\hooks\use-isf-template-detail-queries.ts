import { useQuery } from "@tanstack/react-query";
import { parseAsString, useQueryState } from "nuqs";

import api from "@/lib/apis";

export const isfTemplateDetailKeys = {
  all: (id: string) => ["isf-template-detail", id] as const,

  folders: (id: string) =>
    [...isfTemplateDetailKeys.all(id), "folders"] as const,
  allFiles: (id: string) =>
    [...isfTemplateDetailKeys.all(id), "files"] as const,
  files: (id: string, folderName: string) =>
    [...isfTemplateDetailKeys.allFiles(id), folderName] as const,
};

export const useIsfTemplateFolders = (id: string) =>
  useQuery({
    queryKey: isfTemplateDetailKeys.folders(id),
    queryFn: () => api.isfTemplates.getFolders(id),
  });

export const useIsfTemplateFiles = (id: string) => {
  const [parentDirectoryName] = useQueryState("folderName", parseAsString);
  return useQuery({
    queryKey: isfTemplateDetailKeys.files(id, parentDirectoryName || ""),
    queryFn: () =>
      api.isfTemplates.getFiles({
        id,
        parentDirectoryName: parentDirectoryName || "",
      }),
  });
};
