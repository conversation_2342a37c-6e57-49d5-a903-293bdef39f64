"use client";

import { useAuth } from "@clerk/nextjs";
import type { PropsWithChildren } from "react";
import { useEffect } from "react";
import { AiOutlineLoading } from "react-icons/ai";

import { useAuthentication } from "@/contexts/authentication";
import { useUserTypeStore } from "@/contexts/user-type";
import { useAuthenticated } from "@/hooks/auth";

export const Authenticate = ({ children }: PropsWithChildren) => {
  const { mutate, isPending } = useAuthenticated();
  const { isLoaded, isSignedIn } = useAuth();
  const { userType } = useUserTypeStore();
  const { signOut } = useAuth();

  useEffect(() => {
    if (!isLoaded) return;
    if (isSignedIn) {
      mutate();
    } else {
      useAuthentication.setState({
        authenticated: false,
        user: undefined,
        error: undefined,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isLoaded, isSignedIn]);

  useEffect(() => {
    if (userType && userType !== "clincove") {
      let domain = window.location.origin;
      domain = domain.replace("-admin", "-app");
      signOut().then(() => {
        window.location.href = domain;
      });
    }
  }, [userType, signOut]);

  if (!isLoaded || isPending || userType !== "clincove") {
    return (
      <div className="flex h-screen w-screen items-center justify-center">
        <AiOutlineLoading className="h-10 w-10 animate-spin dark:fill-white" />
      </div>
    );
  }

  return children;
};
