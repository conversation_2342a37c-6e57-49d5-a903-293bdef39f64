import { Card } from "flowbite-react";
import { useMemo, useState } from "react";

import { SearchField } from "@/components/shared";
import LoadingWrapper from "@/components/shared/loading-wrapper";
import { TableDataPagination } from "@/components/ui/pagination";
import { TableLoading } from "@/components/ui/table";
import { TableData } from "@/components/ui/table/table";
import { RoleSubject } from "@/lib/apis/roles";

import { generatePermissionSubjectColumns } from "./columns";
import { usePermissionSubjects } from "./hooks/use-permission-subject-queries";
import { SubjectModal } from "./subject-modal";

export const SubjectsTab = () => {
  const { data, isPending, isPlaceholderData } = usePermissionSubjects();

  const [selectedSubject, setSetSelectedSubject] = useState<RoleSubject | null>(
    null,
  );

  const columns = useMemo(
    () =>
      generatePermissionSubjectColumns({
        onView: (data) => {
          setSetSelectedSubject(data);
        },
      }),
    [],
  );

  return (
    <>
      <Card className="[&>div]:p-0">
        <div className="space-y-2 p-4">
          <div className="flex justify-end">
            <div className="max-w-60">
              <SearchField placeholder="Search by name..." />
            </div>
          </div>
        </div>
        {isPending ? (
          <TableLoading columns={columns} />
        ) : (
          <>
            <LoadingWrapper isLoading={isPlaceholderData}>
              <TableData data={data?.results ?? []} columns={columns} />
            </LoadingWrapper>
            {data?.metadata && <TableDataPagination metadata={data.metadata} />}
          </>
        )}
      </Card>
      {!!selectedSubject && (
        <SubjectModal
          selectedSubject={selectedSubject}
          isOpen={!!selectedSubject}
          onClose={() => setSetSelectedSubject(null)}
        />
      )}
    </>
  );
};
