import { Card } from "flowbite-react";
import { useParams } from "next/navigation";
import { useState } from "react";
import { IoMdAdd } from "react-icons/io";

import { Button } from "@/components/ui/button";
import { Table } from "@/components/ui/table";

import { useAssignmentStudies } from "../../../hooks/use-assignment-studies";
import { columns } from "./columns";
import { ModalAddStudies } from "./modal-add-studies";

export const StudiesTab = () => {
  const params = useParams();
  const { data, isLoading } = useAssignmentStudies(params.id as string);

  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <Card className="[&>div]:p-0">
        <div className="flex items-center justify-between p-4">
          <div className="text-lg font-semibold">Studies</div>
          <Button variant="primary" onClick={() => setIsOpen(true)}>
            <IoMdAdd />
            Add Study
          </Button>
        </div>
        {isLoading ? (
          <Table
            columns={columns}
            data={Array.from({ length: 5 }).map(
              (_, idx) => ({ id: idx }) as any,
            )}
            isLoading
          />
        ) : (
          <Table columns={columns} data={data?.results ?? []} />
        )}
        <ModalAddStudies isOpen={isOpen} onClose={() => setIsOpen(false)} />
      </Card>
    </>
  );
};
