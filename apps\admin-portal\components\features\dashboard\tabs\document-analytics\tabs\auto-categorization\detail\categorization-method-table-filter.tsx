"use client";

import { ListFilter } from "lucide-react";
import { useRef, useState } from "react";
import { z } from "zod";

import { useInfiniteSponsors } from "@/components/features/sponsors/hooks/use-sponsors";
import { Button } from "@/components/ui/button";
import {
  Dropdown,
  DropdownContent,
  DropdownTrigger,
} from "@/components/ui/dropdown";
import { DateRangePicker, Form, FormRef } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { LazySelectV2 } from "@/components/ui/lazy-select/lazy-select-v2";
import { UncontrolledSelect } from "@/components/ui/form/select/uncontrolled-select";
import { useInfiniteStudiesBySponsor } from "@/hooks/use-studies";
import { cn } from "@/lib/utils";

import { useAutoCategorizationFilters } from "../hooks/use-auto-categorization-filters";

const schema = z.object({
  sponsorId: z.string().optional().nullable(),
  studyId: z.string().optional().nullable(),
  methodType: z.string().optional().nullable(),
  status: z.string().optional().nullable(),
  dateRange: z
    .object({
      from: z.date().optional().nullable(),
      to: z.date().optional().nullable(),
    })
    .optional()
    .nullable(),
});

const METHOD_TYPE_OPTIONS = [
  { label: "All Types", value: "" },
  { label: "AI", value: "ai" },
  { label: "Rule-based", value: "rule-based" },
  { label: "Hybrid", value: "hybrid" },
];

const STATUS_OPTIONS = [
  { label: "All Statuses", value: "" },
  { label: "Active", value: "active" },
  { label: "Inactive", value: "inactive" },
  { label: "Draft", value: "draft" },
];

export const CategorizationMethodTableFilter = () => {
  const [open, setOpen] = useState(false);

  const {
    sponsorId,
    setSponsorId,
    studyId,
    setStudyId,
    methodType,
    setMethodType,
    status,
    setStatus,
    fromDate,
    setFromDate,
    toDate,
    setToDate,
    goToPage,
  } = useAutoCategorizationFilters();

  const [selectedSponsorId, setSelectedSponsorId] = useState(sponsorId || "");

  const formRef = useRef<FormRef<z.infer<typeof schema>>>(null);

  const { data: sponsors } = useInfiniteSponsors({
    searchTerm: "",
  });

  const { data: studies } = useInfiniteStudiesBySponsor({
    sponsorId: selectedSponsorId,
    searchTerm: "",
  });

  const activeFiltersCount = [sponsorId, studyId, methodType, status, fromDate, toDate].filter(
    Boolean,
  ).length;

  const handleApplyFilters = (data: z.infer<typeof schema>) => {
    setSponsorId(data.sponsorId || null);
    setStudyId(data.studyId || null);
    setMethodType(data.methodType || null);
    setStatus(data.status || null);
    setFromDate(data.dateRange?.from?.toISOString().split("T")[0] || null);
    setToDate(data.dateRange?.to?.toISOString().split("T")[0] || null);
    goToPage(1);
    setOpen(false);
  };

  const handleClearFilters = () => {
    setSponsorId(null);
    setStudyId(null);
    setMethodType(null);
    setStatus(null);
    setFromDate(null);
    setToDate(null);
    goToPage(1);
    formRef.current?.formHandler.reset();
  };

  return (
    <Dropdown open={open} onOpenChange={setOpen}>
      <DropdownTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={cn(
            "relative flex items-center gap-2",
            activeFiltersCount > 0 && "border-blue-500 text-blue-600",
          )}
        >
          <ListFilter className="h-4 w-4" />
          Filter
          {activeFiltersCount > 0 && (
            <span className="absolute -right-2 -top-2 flex h-5 w-5 items-center justify-center rounded-full bg-blue-500 text-xs font-medium text-white">
              {activeFiltersCount}
            </span>
          )}
        </Button>
      </DropdownTrigger>
      <DropdownContent className="w-80 p-4" align="end">
        <Form
          ref={formRef}
          schema={schema}
          onSubmit={handleApplyFilters}
          defaultValues={{
            sponsorId: sponsorId || "",
            studyId: studyId || "",
            methodType: methodType || "",
            status: status || "",
            dateRange: {
              from: fromDate ? new Date(fromDate) : null,
              to: toDate ? new Date(toDate) : null,
            },
          }}
        >
          <div className="space-y-4">
            <div>
              <Label htmlFor="sponsorId">Sponsor</Label>
              <LazySelectV2
                name="sponsorId"
                placeholder="Select sponsor"
                data={sponsors}
                onValueChange={(value) => {
                  setSelectedSponsorId(value);
                  formRef.current?.formHandler.setValue("studyId", "");
                }}
              />
            </div>

            <div>
              <Label htmlFor="studyId">Study</Label>
              <LazySelectV2
                name="studyId"
                placeholder="Select study"
                data={studies}
                disabled={!selectedSponsorId}
              />
            </div>

            <div>
              <Label htmlFor="methodType">Method Type</Label>
              <UncontrolledSelect
                name="methodType"
                placeholder="Select method type"
                options={METHOD_TYPE_OPTIONS}
              />
            </div>

            <div>
              <Label htmlFor="status">Status</Label>
              <UncontrolledSelect
                name="status"
                placeholder="Select status"
                options={STATUS_OPTIONS}
              />
            </div>

            <div>
              <Label htmlFor="dateRange">Date Range</Label>
              <DateRangePicker name="dateRange" />
            </div>

            <div className="flex gap-2 pt-2">
              <Button type="submit" size="sm" className="flex-1">
                Apply Filters
              </Button>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleClearFilters}
                className="flex-1"
              >
                Clear All
              </Button>
            </div>
          </div>
        </Form>
      </DropdownContent>
    </Dropdown>
  );
};
