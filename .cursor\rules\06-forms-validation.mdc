---
description:
globs:
alwaysApply: true
---

# Forms and Validation Guidelines

## Core Libraries
- Use React Hook Form for form state management
- Use Zod for form validation
- Use Flowbite React form components

## Form Structure
- Create small, focused form components
- Use proper form HTML structure
- Group related form fields
- Implement proper form layouts
- Use fieldsets for related fields
- Provide clear form instructions

## Form State Management
- Use React Hook Form for form state
- Implement proper form validation
- Show appropriate error messages
- Handle form submission properly
- Show appropriate loading states during submission
- Provide clear feedback for form errors and success

## Validation
- Use Zod for form validation
- Define validation schemas separately
- Reuse validation schemas when possible
- Implement proper error messages
- Show inline validation errors
- Validate on blur or change as appropriate
- Implement proper form-level validation

## Error Handling
- Show appropriate error messages
- Position error messages consistently
- Use proper error styling
- Ensure error messages are accessible
- Implement proper form-level error handling
- Scroll to errors when form is submitted

## Form Components
- Use consistent form components
- Implement proper label associations
- Show required field indicators
- Implement proper form control states (focus, error, disabled, etc.)
- Use proper input types for different data
- Implement proper form layouts

## Form Submission
- Implement proper form submission handling
- Show appropriate loading states during submission
- Disable submit button during submission
- Provide clear feedback for form errors and success
- Implement proper error handling for submission
- Redirect or show success message after submission

## Complex Form Patterns
- Implement proper multi-step forms
- Handle form arrays properly
- Implement proper conditional fields
- Handle dependent fields properly
- Implement proper form wizards
- Handle form state persistence

## Accessibility
- Ensure all form components are accessible
- Use proper label associations
- Provide proper ARIA attributes
- Ensure keyboard navigation works
- Test with screen readers
- Implement proper focus management
