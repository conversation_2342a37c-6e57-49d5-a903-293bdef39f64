import BaseApi from "../base";
import type {
  DatabaseGrowthResponse,
  HmacFailuresListResponse,
  HmacVerificationFailuresResponse,
  OfflineScannersResponse,
  ProcessingErrorsListResponse,
  ProcessingErrorSource,
  SystemProcessingErrorsResponse,
} from "./types";

export * from "./types";

class SystemHealthApi extends BaseApi {
  constructor() {
    super("/system-health", true);
  }

  public async getProcessingErrors() {
    return this.http.get<SystemProcessingErrorsResponse>("/processing-errors");
  }

  public async getProcessingErrorsList(params?: {
    page?: number;
    take?: number;
    sources?: ProcessingErrorSource[];
  }) {
    return this.http.get<ProcessingErrorsListResponse>(
      "/processing-errors/list",
      params,
    );
  }

  public async getHmacFailures() {
    return this.http.get<HmacVerificationFailuresResponse>("/hmac-failures");
  }

  public async getHmacFailuresList(params?: { page?: number; take?: number }) {
    return this.http.get<HmacFailuresListResponse>(
      "/hmac-failures/list",
      params,
    );
  }

  public async getOfflineScanners(threshold: number = 24) {
    return this.http.get<OfflineScannersResponse>("/offline-scanners", {
      threshold,
    });
  }

  public async getDatabaseGrowth(params?: {
    fromDate?: string;
    toDate?: string;
    unit?: "day" | "week";
    combine?: boolean;
  }) {
    return this.http.get<DatabaseGrowthResponse>("/database-growth", params);
  }
}

export const systemHealth = new SystemHealthApi();
