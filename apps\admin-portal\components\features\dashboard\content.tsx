"use client";

import { TabsRef } from "flowbite-react";
import { parseAsString, useQueryState } from "nuqs";
import { useRef } from "react";
import {
  FaChartBar,
  FaClipboardList,
  FaCog,
  FaHeartbeat,
  FaServer,
  FaUsers,
} from "react-icons/fa";

import { UncontrolledSelect } from "@/components/ui/form/select/uncontrolled-select";
import { TabsWrapper } from "@/components/ui/tabs-wrapper";

import { AuditLogTab } from "./tabs/audit-log";
import { ConfigurationsTab } from "./tabs/configurations";
import { DeviceManagementTab } from "./tabs/device-management";
import { AnalyticsTab } from "./tabs/document-analytics";
import { DocumentAnalyticsTab } from "./tabs/document-analytics/tabs/document-analytics";
import { SystemVitalsTab } from "./tabs/system-health";
import { UserManagementTab } from "./tabs/user-management";

export const DashboardContent = () => {
  const [currentTab, setCurrentTab] = useQueryState("tab", parseAsString);
  const tabRef = useRef<TabsRef | null>(null);

  const handleNavigateToDevices = () => {
    tabRef.current?.setActiveTab(4);
  };

  const DASHBOARD_TABS = [
    {
      key: "system-health",
      title: (
        <p className="flex items-center gap-4">
          <FaHeartbeat className="h-5 w-5 text-red-500" />
          System Health
        </p>
      ),
      content: (
        <SystemVitalsTab onNavigateToDevices={handleNavigateToDevices} />
      ),
    },
    {
      key: "user-management",
      title: (
        <p className="flex items-center gap-4">
          <FaUsers className="h-5 w-5 text-blue-500" />
          User Management
        </p>
      ),
      content: <UserManagementTab />,
    },
    {
      key: "configurations",
      title: (
        <p className="flex items-center gap-4">
          <FaCog className="h-5 w-5 text-gray-500" />
          Configurations
        </p>
      ),
      content: <ConfigurationsTab />,
    },
    {
      key: "audit-log",
      title: (
        <p className="flex items-center gap-4">
          <FaClipboardList className="h-5 w-5 text-green-500" />
          Audit Logs
        </p>
      ),
      content: <AuditLogTab />,
    },
    {
      key: "device-management",
      title: (
        <p className="flex items-center gap-4">
          <FaServer className="h-5 w-5 text-purple-500" />
          Device Management
        </p>
      ),
      content: <DeviceManagementTab />,
    },
    {
      key: "analytics",
      title: (
        <p className="flex items-center gap-4">
          <FaChartBar className="h-5 w-5 text-orange-500" />
          Analytics
        </p>
      ),
      content: <AnalyticsTab />,
    },
  ] as const;

  const SELECT_OPTIONS = DASHBOARD_TABS.map((tab) => ({
    label: tab.title,
    value: tab.key,
  }));

  return (
    <div className="space-y-4">
      <span
        onClick={() => tabRef.current?.setActiveTab(2)}
        className="text-2xl font-semibold text-gray-900 sm:text-3xl dark:text-white"
      >
        Dashboard
      </span>

      <div className="space-y-4 sm:hidden">
        <UncontrolledSelect
          placeholder="Select dashboard section"
          options={SELECT_OPTIONS}
          value={currentTab || ""}
          onChange={(value) => {
            if (value) {
              setCurrentTab(value);
            }
          }}
          className="w-full"
        />

        <div>
          {DASHBOARD_TABS.find((tab) => tab.key === currentTab)?.content ??
            DASHBOARD_TABS[0].content}
        </div>
      </div>

      <div className="hidden sm:block">
        <TabsWrapper
          tabRef={tabRef}
          className="flex-nowrap overflow-x-auto [&>button]:flex-shrink-0"
          tabs={DASHBOARD_TABS}
        />
      </div>
    </div>
  );
};
