import { Procedure } from "../procedures";
import { ListBaseResponse } from "../types";

export interface Activity {
  id: string;
  name: string;
  description: string;
  isSystem: boolean;
  isActive: boolean;
  studyId: string;
  activityProcedures: {
    id: string;
    createdDate: Date;
    lastUpdatedDate: Date;
    activityId: string;
    procedureId: string;
    procedure: ProcedureDetail;
  }[];
}

export type ProcedureDetail = {
  id: string;
  createdDate: Date;
  lastUpdatedDate: Date;
  name: string;
  description: null;
  isActive: boolean;
  studyId: null;
};

export type ActivityProcedure = Procedure;

export type ActivityProceduresResponse = ListBaseResponse<Procedure>;
export type ActivityListResponse = ListBaseResponse<Activity>;

export type UpdateActivityStatusPayload = {
  isActive: boolean;
};

export type UpdateActivityPayload = {
  name: string;
  description?: string;
  isActive?: boolean;
  studyId?: string;
  isSystem?: boolean;
};

export type CreateActivityPayload = {
  name: string;
  description?: string;
  isActive?: boolean;
  studyId: string;
};

export type RemoveProcedureFromActivityPayload = {
  activityId: string;
  procedureId: string;
};

export type CreateActivityProcedurePayload = {
  name: string;
  description?: string;
  isActive?: boolean;
  studyId: string;
  encounterId: string;
};

export type AddActivityProcedurePayload = {
  activityId: string;
  procedureId: string;
};

export type ReorderActivityPayload = {
  studyId: string;
  orderedIds: string[];
};

///

export type UserStatisticResponse = {
  totalUsers: number;
  activeUsers: number;
  newUsers: number;
  lockedUsers: number;
};

export type RoleDistribution = {
  roleId: string;
  roleName: string;
  count: number;
};

export type RoleDistributionResponse = RoleDistribution[];

export type User = {
  id: string;
  name: string;
};

export type UsersWithoutProfileResponse = ListBaseResponse<User>;
export type ProfileWithoutRoleResponse = ListBaseResponse<User>;
export type UserNeverLoginResponse = ListBaseResponse<User>;

export type LoginActivity = {
  date: string;
  loginCount: number;
};

export type LoginActivityResponse = LoginActivity[];
