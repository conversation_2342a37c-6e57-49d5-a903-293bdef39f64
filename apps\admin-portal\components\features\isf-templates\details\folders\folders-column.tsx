import { Plus } from "lucide-react";
import { useParams } from "next/navigation";
import { parseAsString, useQueryState } from "nuqs";
import React, { useEffect, useState } from "react";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { useDisclosure } from "@/hooks/use-disclosure";
import { Directory, File } from "@/lib/apis/isf-templates";

import { FileWithParentDirectoryName } from "../documents/documents-table";
import { useDeleteFolder } from "../hooks/use-isf-template-detail-mutations";
import { useIsfTemplateFolders } from "../hooks/use-isf-template-detail-queries";
import { FolderModal } from "./folder-modals";
import { FolderNode } from "./folder-node";

type Props = {
  draggingFolder: Directory | null;
  draggingFile: FileWithParentDirectoryName | null;
  isMovingFolder: boolean;
  isMovingDocument: boolean;
  activePath: string;
  setActivePath: React.Dispatch<React.SetStateAction<string>>;
};

export const FolderColumn = ({
  draggingFolder,
  isMovingFolder,
  draggingFile,
  isMovingDocument,
  activePath,
  setActivePath,
}: Props) => {
  const params = useParams();
  const id = params.id as string;
  const [folderName, setFolderName] = useQueryState(
    "folderName",
    parseAsString,
  );
  const {
    data: folders,
    isLoading: isLoadingFolder,
    isSuccess,
  } = useIsfTemplateFolders(id);
  const [selectedFolder, setSelectedFolder] = useState<Directory | null>(null);
  const { mutate: deleteFolder, isPending: isDeleting } = useDeleteFolder(id);

  const {
    isOpen: isFolderModalOpen,
    open: onFolderModalOpen,
    close: onFolderModalClose,
  } = useDisclosure();

  const handleOnDelete = (folder: Directory) => {
    deleteFolder({ folderName: folder.name });
  };

  const handleEdit = (data: Directory) => {
    setSelectedFolder(data);
    onFolderModalOpen();
  };

  useEffect(() => {
    if (folderName) return;
    if (isSuccess) {
      const isExistFolder = !!folders.directories?.[0]?.name;

      setFolderName(isExistFolder ? folders.directories[0].name : null);
      setActivePath(isExistFolder ? `/${folders.directories[0].name}` : "");
    }
  }, [folders, isSuccess, setFolderName]);

  return (
    <>
      <div className="flex w-full flex-col overflow-hidden rounded-lg border border-gray-300 md:h-full dark:border-gray-500 [&>div:last-child]:flex-1">
        <div className="p-5 pb-2.5">
          <Button
            className="[&>span]:!px-0"
            variant="outline"
            onClick={onFolderModalOpen}
          >
            <Plus className="size-4" />
            <span className="min-w-0 truncate">New Folder</span>
          </Button>
        </div>

        {isLoadingFolder ? (
          <FolderSkeleton />
        ) : (
          <LoadingWrapper
            isLoading={isMovingFolder || isMovingDocument || isDeleting}
          >
            <div className="overflow-hidden">
              {folders?.directories?.map((folder) => (
                <FolderNode
                  key={folder.name}
                  folder={folder}
                  level={0}
                  draggingFolder={draggingFolder}
                  draggingFile={draggingFile}
                  path={`/${folder.name}`}
                  activePath={activePath}
                  setActivePath={setActivePath}
                  onDelete={handleOnDelete}
                  onEdit={handleEdit}
                  rootPath={`/${folder.name}`}
                />
              ))}
            </div>
          </LoadingWrapper>
        )}
      </div>
      <FolderModal
        selectedFolder={selectedFolder}
        isOpen={isFolderModalOpen}
        onClose={() => {
          onFolderModalClose();
          setSelectedFolder(null);
        }}
      />
    </>
  );
};

const FolderSkeleton = () => (
  <div className="flex-1">
    {[1, 2, 3, 4].map((i) => (
      <div key={i} className="py-2 pl-4 pr-3">
        <Skeleton className="h-7 w-full " />
      </div>
    ))}
  </div>
);
