import { useQuery } from "@tanstack/react-query";
import { SortingState } from "@tanstack/react-table";

import { useSort } from "@/hooks/use-sort";
import api from "@/lib/apis";
import type { AutoCategorizationFilters } from "@/lib/apis/auto-categorization";
import { MetadataParams } from "@/lib/apis/types";

import { useAutoCategorizationFilters } from "./use-auto-categorization-filters";

const TAKE_50 = { take: 50 };

const autoCategorizationKeys = {
  all: () => ["auto-categorization"] as const,

  allStats: () => [...autoCategorizationKeys.all(), "stats"] as const,
  methodStats: (filters: AutoCategorizationFilters) =>
    [...autoCategorizationKeys.allStats(), "methods", filters] as const,
  performance: (filters: AutoCategorizationFilters) =>
    [...autoCategorizationKeys.allStats(), "performance", filters] as const,
  methodTypeDistribution: (filters: AutoCategorizationFilters) =>
    [
      ...autoCategorizationKeys.allStats(),
      "method-type-distribution",
      filters,
    ] as const,

  allMethods: () => [...autoCategorizationKeys.all(), "methods"] as const,
  methodsList: (filters: AutoCategorizationFilters & MetadataParams) =>
    [...autoCategorizationKeys.allMethods(), "list", filters] as const,
  methodDetail: (id: string, filters: AutoCategorizationFilters) =>
    [...autoCategorizationKeys.allMethods(), "detail", id, filters] as const,
};

// Stats hooks
export const useCategorizationMethodStats = () => {
  const { filters } = useAutoCategorizationFilters();

  return useQuery({
    queryKey: autoCategorizationKeys.methodStats(filters),
    queryFn: () =>
      api.autoCategorizationApi.getCategorizationMethodStats(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useCategorizationPerformance = () => {
  const { filters } = useAutoCategorizationFilters();

  return useQuery({
    queryKey: autoCategorizationKeys.performance(filters),
    queryFn: () =>
      api.autoCategorizationApi.getCategorizationPerformance(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useMethodTypeDistribution = () => {
  const { filters } = useAutoCategorizationFilters();

  return useQuery({
    queryKey: autoCategorizationKeys.methodTypeDistribution(filters),
    queryFn: () => api.autoCategorizationApi.getMethodTypeDistribution(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Methods list hook
export const useCategorizationMethods = (
  sorting?: SortingState,
  pagination?: MetadataParams,
) => {
  const { filters } = useAutoCategorizationFilters();

  const queryParams = {
    ...filters,
    ...pagination,
    ...TAKE_50,
  };

  return useQuery({
    queryKey: autoCategorizationKeys.methodsList(queryParams),
    queryFn: () =>
      api.autoCategorizationApi.getCategorizationMethods(queryParams),
    placeholderData: (previousData) => previousData,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Method detail hook
export const useCategorizationMethodDetail = (id: string) => {
  const { filters } = useAutoCategorizationFilters();

  return useQuery({
    queryKey: autoCategorizationKeys.methodDetail(id, filters),
    queryFn: () =>
      api.autoCategorizationApi.getCategorizationMethodDetail(id, filters),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};
