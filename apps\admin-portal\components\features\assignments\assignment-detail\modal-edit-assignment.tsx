import { useParams } from "next/navigation";
import type { z } from "zod";

import { <PERSON><PERSON>, CloseButton } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { Modal } from "@/components/ui/modal";
import type { Assignment } from "@/lib/apis/assignments/types";

import {
  addAssignmentSchema,
  AssignmentForm,
} from "../default/modal-add-assignment";
import { useEditAssignment } from "../hooks/use-edit-assignment";

type ModalEditAssignmentProps = {
  isOpen: boolean;
  onClose: () => void;
  assignment?: Assignment;
};

const editAssignmentSchema = addAssignmentSchema;

export const ModalEditAssignment = ({
  isOpen,
  onClose,
  assignment,
}: ModalEditAssignmentProps) => {
  const params = useParams();
  const assignmentId = params.id as string;
  const { mutateAsync: editAssignment, isPending: isEditing } =
    useEditAssignment();

  async function onSubmit(data: z.infer<typeof editAssignmentSchema>) {
    await editAssignment({ id: assignmentId, ...data });
    onClose();
  }

  return (
    <Modal show={isOpen} onClose={onClose}>
      <Modal.Header>Edit Assignment</Modal.Header>
      <Modal.Body>
        <Form
          mode="onChange"
          schema={editAssignmentSchema}
          onSubmit={onSubmit}
          formProps={{ shouldFocusError: false }}
          defaultValues={{
            ...assignment,
            groupId: assignment?.group?.id,
            siteId: assignment?.profile?.siteId,
          }}
        >
          <AssignmentForm />
          <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
            <CloseButton onClose={onClose} />
            <Button
              type="submit"
              color="blue"
              disabled={isEditing}
              isLoading={isEditing}
              enabledForDirty
            >
              Update Assignment
            </Button>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
};
