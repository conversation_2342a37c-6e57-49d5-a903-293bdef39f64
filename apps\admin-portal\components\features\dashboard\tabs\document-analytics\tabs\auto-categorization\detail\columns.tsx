import { ColumnDef } from "@tanstack/react-table";

import { PillBadge } from "@/components/ui/badges/pill-badge";
import { CategorizationMethod } from "@/lib/apis/auto-categorization";
import { formatDate } from "@/lib/utils";

const getStatusBadgeColor = (status: string) => {
  switch (status) {
    case "active":
      return "green";
    case "inactive":
      return "gray";
    case "draft":
      return "yellow";
    default:
      return "gray";
  }
};

const getTypeBadgeColor = (type: string) => {
  switch (type) {
    case "ai":
      return "blue";
    case "rule-based":
      return "purple";
    case "hybrid":
      return "indigo";
    default:
      return "gray";
  }
};

export const categorizationMethodColumns: ColumnDef<CategorizationMethod>[] = [
  {
    accessorKey: "name",
    header: "Method Name",
    cell: ({ row }) => (
      <div className="flex flex-col">
        <span className="font-medium text-gray-900 dark:text-white">
          {row.original.name}
        </span>
        {row.original.description && (
          <span className="text-sm text-gray-500 dark:text-gray-400">
            {row.original.description}
          </span>
        )}
      </div>
    ),
  },
  // {
  //   accessorKey: "type",
  //   header: "Type",
  //   cell: ({ row }) => (
  //     <PillBadge
  //       // variant={getTypeBadgeColor(row.original.type)}
  //       variant={getTypeBadgeColor(row.original.type)}

  //       text={row.original.type.toUpperCase()}
  //     />
  //   ),
  // },
  // {
  //   accessorKey: "status",
  //   header: "Status",
  //   cell: ({ row }) => (
  //     <PillBadge
  //       color={getStatusBadgeColor(row.original.status)}
  //       text={row.original.status.toUpperCase()}
  //     />
  //   ),
  // },
  {
    accessorKey: "accuracy",
    header: "Accuracy",
    cell: ({ row }) => (
      <span className="font-medium">
        {(row.original.accuracy * 100).toFixed(1)}%
      </span>
    ),
  },
  {
    accessorKey: "totalCategorizations",
    header: "Total Uses",
    cell: ({ row }) => (
      <span className="text-gray-900 dark:text-white">
        {row.original.totalCategorizations.toLocaleString()}
      </span>
    ),
  },
  {
    accessorKey: "averageConfidence",
    header: "Avg Confidence",
    cell: ({ row }) => (
      <span className="font-medium">
        {(row.original.averageConfidence * 100).toFixed(1)}%
      </span>
    ),
  },
  {
    accessorKey: "version",
    header: "Version",
    cell: ({ row }) => row.original.version,
  },
  {
    accessorKey: "lastUsedDate",
    header: "Last Used",
    cell: ({ row }) =>
      row.original.lastUsedDate
        ? formatDate(row.original.lastUsedDate, "MMM dd, yyyy")
        : "-",
  },
];
