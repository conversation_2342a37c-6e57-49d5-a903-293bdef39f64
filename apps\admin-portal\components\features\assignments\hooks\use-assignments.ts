import { useQuery } from "@tanstack/react-query";

import { usePagination } from "@/hooks/use-pagination";
import { useSearch } from "@/hooks/use-search";
import api from "@/lib/apis";

export const USE_ASSIGNMENTS_QUERY_KEY = "assignments";

export const useAssignments = () => {
  const { page, take } = usePagination();
  const { search } = useSearch();

  return useQuery({
    queryKey: [USE_ASSIGNMENTS_QUERY_KEY, page, take, search],
    queryFn: () =>
      api.assignments.list({
        page,
        take,
        filter: { name: search },
      }),
  });
};
