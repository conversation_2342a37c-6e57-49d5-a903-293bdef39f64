"use client";

import { useAuth } from "@clerk/nextjs";
import { sdkSetURL, sdkSetUser } from "@clincove-eng/backend-sdk-temp";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

import { configs } from "@/lib/config";
import { CLERK_TOKEN_TEMPLATE } from "@/lib/constants";

export default function Page() {
  const { isLoaded, userId, getToken } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (isLoaded && userId) {
      getToken({ template: CLERK_TOKEN_TEMPLATE }).then((token) => {
        if (token) {
          sdkSetURL(configs.API_URL ?? "");
          sdkSetUser(token);
        }
      });
    }
  }, [getToken, isLoaded, router, userId]);

  // In case the user signs out while on the page.
  if (!isLoaded || !userId) {
    return <div>Loading...</div>;
  }

  return null;
}
