import { useQuery } from "@tanstack/react-query";

import { usePagination } from "@/hooks/use-pagination";
import api from "@/lib/apis";
import { MetadataParams } from "@/lib/apis/types";

const useCategorizationMethodsKeys = {
  all: () => ["auto-categorization", "methods"] as const,
  list: (params?: MetadataParams) =>
    [...useCategorizationMethodsKeys.all(), params] as const,
};

export const TAKE_50 = 50;

export const useCategorizationMethods = () => {
  const { page } = usePagination();
  const params = {
    page,
    take: TAKE_50,
  };
  
  return useQuery({
    queryKey: useCategorizationMethodsKeys.list(params),
    queryFn: () => api.autoCategorizationApi.getCategorizationMethods(params),
    placeholderData: (prev) => prev,
  });
};
