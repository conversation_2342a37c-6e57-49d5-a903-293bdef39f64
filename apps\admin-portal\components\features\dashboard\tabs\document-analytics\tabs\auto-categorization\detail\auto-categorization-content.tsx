"use client";

import { parseAsString, useQueryState } from "nuqs";
import { MdOutlineBrightnessAuto } from "react-icons/md";

import { BackButton } from "@/components/shared";
import { UncontrolledSelect } from "@/components/ui/form/select/uncontrolled-select";
import { TabsWrapper } from "@/components/ui/tabs-wrapper";

import { CategorizationMethodTable } from "./categorization-method-table";

const AUTO_CATEGORIZATION_TABS = [
  {
    key: "methods",
    title: (
      <p className="flex items-center gap-3">
        <MdOutlineBrightnessAuto className="h-5 w-5 text-purple-500" />
        Categorization Methods
      </p>
    ),
    content: <CategorizationMethodTable />,
  },
] as const;

const SELECT_OPTIONS = AUTO_CATEGORIZATION_TABS.map((tab) => ({
  label: tab.title,
  value: tab.key,
}));

export const AutoCategorizationContent = () => {
  const [currentTab, setCurrentTab] = useQueryState("tab", parseAsString);

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <BackButton type="link" href="/dashboards?tab=document-analytics" />
        <h1 className="text-xl font-bold text-gray-900 sm:text-2xl dark:text-white">
          Auto Categorization Dashboard
        </h1>
      </div>

      <div className="block sm:hidden">
        <UncontrolledSelect
          options={SELECT_OPTIONS}
          value={currentTab || "methods"}
          onValueChange={setCurrentTab}
          placeholder="Select tab"
        />
      </div>

      <TabsWrapper
        tabs={AUTO_CATEGORIZATION_TABS}
        activeTab={currentTab || "methods"}
        onTabChange={setCurrentTab}
        className="hidden sm:block"
      />
    </div>
  );
};
