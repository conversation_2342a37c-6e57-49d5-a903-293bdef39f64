import { SignedOut } from "@clerk/nextjs";
import { Metadata } from "next";
import dynamic from "next/dynamic";

export const metadata: Metadata = {
  title: "Sign In - Clincove",
  description: "Sign in to your account",
};

const SignInPageContent = dynamic(
  () =>
    import("@/components/features/sign-in").then(
      (mod) => mod.SignInPageContent,
    ),
  { ssr: false },
);

export default function SignInPage() {
  return (
    <SignedOut>
      <SignInPageContent />
    </SignedOut>
  );
}
