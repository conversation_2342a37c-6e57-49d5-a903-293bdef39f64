"use client";

import { Card } from "flowbite-react";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";

import { tasksInactiveColumns } from "./columns";
import { useTasksInactive } from "./use-tasks-inactive-queries";

export const TasksInactiveUsersGroupsTab = () => {
  const {
    data: tasksData,
    isPending: isLoadingTasks,
    isPlaceholderData: isTasksPlaceholderData,
  } = useTasksInactive();

  return (
    <div>
      <Card className="[&>div]:p-0">
        <h4 className="p-4 text-base font-medium text-gray-900 dark:text-white">
          Tasks with Inactive Users/Groups
        </h4>

        {isLoadingTasks ? (
          <TableLoading columns={tasksInactiveColumns} />
        ) : (
          <LoadingWrapper isLoading={isTasksPlaceholderData}>
            <Table
              columns={tasksInactiveColumns}
              data={tasksData?.results || []}
            />

            {tasksData?.metadata && (
              <TableDataPagination metadata={tasksData.metadata} />
            )}
          </LoadingWrapper>
        )}
      </Card>
    </div>
  );
};
