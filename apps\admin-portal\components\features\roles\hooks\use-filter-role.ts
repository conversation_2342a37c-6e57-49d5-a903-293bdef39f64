import { parseAsString, useQueryState } from "nuqs";

import { usePagination } from "@/hooks/use-pagination";
import { useSearch } from "@/hooks/use-search";

export const useFilterRole = () => {
  const { page, take } = usePagination();
  const { search } = useSearch();
  const [type, setType] = useQueryState("type", parseAsString);
  const [isActive, setIsActive] = useQueryState("active", parseAsString);
  return {
    page,
    take,
    search,
    type,
    setType,
    isActive,
    setIsActive,
  };
};
