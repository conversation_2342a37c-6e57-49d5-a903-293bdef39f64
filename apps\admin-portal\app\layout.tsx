import "./globals.css";

import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/nextjs";
import { Flowbite, ThemeModeScript } from "flowbite-react";
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import { NextIntlClientProvider } from "next-intl";
import { getLocale, getMessages } from "next-intl/server";
import { NuqsAdapter } from "nuqs/adapters/next/app";
import type { PropsWithChildren } from "react";
import { Toaster } from "react-hot-toast";
import { twMerge } from "tailwind-merge";

import { Providers } from "@/contexts";

import { customTheme } from "./theme";

const inter = Inter({ subsets: ["latin"], display: "swap" });

export const metadata: Metadata = {
  title: "Clincove",
};

export default async function RootLayout({ children }: PropsWithChildren) {
  const locale = await getLocale();

  // Providing all messages to the client
  // side is the easiest way to get started
  const messages = await getMessages();

  return (
    <ClerkProvider
      appearance={{
        variables: { colorPrimary: "#1D4ED8" },
      }}
    >
      <html lang={locale}>
        <head>
          <ThemeModeScript />
        </head>
        <body
          className={twMerge("bg-gray-100 dark:bg-gray-900", inter.className)}
        >
          <NuqsAdapter>
            <NextIntlClientProvider messages={messages}>
              <Providers>
                <Flowbite theme={{ theme: customTheme }}>{children}</Flowbite>
              </Providers>
              <Toaster />
            </NextIntlClientProvider>
          </NuqsAdapter>
        </body>
      </html>
    </ClerkProvider>
  );
}
