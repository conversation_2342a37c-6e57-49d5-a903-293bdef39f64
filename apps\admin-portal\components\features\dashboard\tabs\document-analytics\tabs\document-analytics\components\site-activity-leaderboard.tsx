"use client";

import { ColumnDef, SortingState } from "@tanstack/react-table";
import { Card, TextInput } from "flowbite-react";
import Link from "next/link";
import { useState } from "react";
import { HiSearch } from "react-icons/hi";
import { useDebounceCallback } from "usehooks-ts";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { PillBadge } from "@/components/ui/badges/pill-badge";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";
import { SiteActivity } from "@/lib/apis/document-analytics";
import { formatDate } from "@/lib/utils";

import { useSiteActivityLeaderboard } from "../hooks/use-document-analytics-queries";

const columns: ColumnDef<SiteActivity>[] = [
  {
    accessorKey: "siteName",
    header: "Site Name",
    cell: ({ row }) => (
      <Link
        href={`/sites/${row.original.siteId}`}
        className="text-primary-500 flex cursor-pointer flex-col text-left hover:underline"
      >
        {row.original.siteName}
      </Link>
    ),
  },
  {
    accessorKey: "studyName",
    header: "Study",
    cell: ({ row }) => (
      <Link
        href={`/sites/${row.original.siteId}/studies/${row.original.studyId}`}
        className="text-primary-500 flex cursor-pointer flex-col text-left hover:underline"
      >
        {row.original.studyName}
      </Link>
    ),
  },
  {
    accessorKey: "totalDocs",
    header: "Total Docs",
  },
  {
    accessorKey: "docsLast30Days",
    header: "Last 30 Days",
  },
  {
    accessorKey: "usersUploading",
    header: "Active Users",
  },
  {
    accessorKey: "avgTimeToProcess",
    header: "Avg Process Time",
  },

  {
    accessorKey: "isSiteActive",
    header: "Status",
    enableSorting: false,
    cell: ({ row }) => (
      <PillBadge variant={row.original.isSiteActive ? "success" : "danger"}>
        {row.original.isSiteActive ? "Active" : "Inactive"}
      </PillBadge>
    ),
  },
  {
    accessorKey: "lastUploadDate",
    header: "Last Upload",
    cell: ({ row }) =>
      row.original.lastUploadDate
        ? formatDate(row.original.lastUploadDate, "LLL dd, yyyy")
        : "-",
  },
];

export const SitePerformanceLeaderboardTable = () => {
  const [sort, setSort] = useState<SortingState>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [search, setSearch] = useState("");
  const debounced = useDebounceCallback((value) => {
    setSearch(value);
    setCurrentPage(1);
  }, 300);
  const { data, isPending, isPlaceholderData } = useSiteActivityLeaderboard({
    page: currentPage,
    search,
    sort,
  });

  return (
    <Card className="[&>div]:p-0">
      <div className="flex items-center justify-between p-4">
        <h3 className=" text-lg font-semibold text-gray-900 dark:text-white">
          Site Activity
        </h3>
        <div className="max-w-80">
          <TextInput
            className="w-full text-sm"
            icon={HiSearch}
            sizing="sm"
            placeholder="Search..."
            onChange={(e) => debounced(e.target.value)}
          />
        </div>
      </div>
      {isPending ? (
        <TableLoading columns={columns} />
      ) : (
        <LoadingWrapper isLoading={isPlaceholderData}>
          <Table
            columns={columns}
            data={data?.results ?? []}
            enableSorting
            sorting={sort}
            onSortingChange={setSort}
          />
          {data?.metadata && (
            <TableDataPagination
              metadata={data?.metadata}
              page={currentPage}
              setPage={setCurrentPage}
            />
          )}
        </LoadingWrapper>
      )}
    </Card>
  );
};
