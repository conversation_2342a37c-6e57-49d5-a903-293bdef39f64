import { useQuery } from "@tanstack/react-query";

import { usePagination } from "@/hooks/use-pagination";
import api from "@/lib/apis";
import { MetadataParams } from "@/lib/apis/types";

const useSitesNoAssignedUserKeys = {
  all: () => ["data-integrity", "sites-no-assigned-user"] as const,
  list: (params?: MetadataParams) =>
    [...useSitesNoAssignedUserKeys.all(), params] as const,
};

export const TAKE_50 = 50;

export const useSitesNoAssignedUser = () => {
  const { page } = usePagination();
  const params = {
    page,
    take: TAKE_50,
  };
  return useQuery({
    queryKey: useSitesNoAssignedUserKeys.list(params),
    queryFn: () => api.dataIntegrity.getSitesNoAssignedUser(params),
    placeholderData: (prev) => prev,
  });
};
