import { ColumnDef } from "@tanstack/react-table";

import {
  TableEditButton,
  TableRemoveButton,
} from "@/components/shared/table-action-buttons";

export const generateISFColumns = ({
  onDelete,
  onEdit,
}: {
  onEdit: (data: any) => void;
  onDelete: (data: any) => void;
}): ColumnDef<any>[] => [
  {
    accessorKey: "id",
    header: "ID",
  },
  {
    accessorKey: "isfRefModel",
    header: "Label",
  },
  {
    accessorKey: "description",
    header: "Description",
  },
  {
    header: "Actions",
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="flex gap-x-4">
          <TableEditButton type="button" onClick={() => onEdit(data)} />
          <TableRemoveButton onClick={() => onDelete(data)} />
        </div>
      );
    },
  },
];
