import { useQueryClient } from "@tanstack/react-query";
import { useParams } from "next/navigation";
import { z } from "zod";

import { useAddUserAssignment } from "@/components/features/users/hooks/use-add-user-assignment";
import { <PERSON><PERSON>, <PERSON>Button } from "@/components/ui/button";
import { Form, Select } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { Modal } from "@/components/ui/modal";
import { Skeleton } from "@/components/ui/skeleton";
import { useAllUsers } from "@/hooks/use-users";

import { USE_ASSIGNMENT_USERS_QUERY_KEY } from "../../../hooks/use-assignment-users";

const addAssignmentUserSchema = z.object({
  userId: z
    .string({ required_error: "Please select a user" })
    .min(1, "Please select a user"),
});

type ModalAddAssignmentUserProps = {
  isOpen: boolean;
  onClose: () => void;
};

export const ModalAddAssignmentUser = ({
  isOpen,
  onClose,
}: ModalAddAssignmentUserProps) => {
  const params = useParams();
  const assignmentId = params.id as string;
  const queryClient = useQueryClient();

  const { mutateAsync: addAssignmentUser, isPending: isAddingAssignmentUser } =
    useAddUserAssignment();

  async function onSubmit(data: z.infer<typeof addAssignmentUserSchema>) {
    await addAssignmentUser({
      id: data.userId,
      assignmentId,
    });
    queryClient.invalidateQueries({
      queryKey: [USE_ASSIGNMENT_USERS_QUERY_KEY, assignmentId],
    });
    onClose();
  }

  return (
    <Modal show={isOpen} onClose={onClose}>
      <Modal.Header>Add User</Modal.Header>
      <Modal.Body>
        <Form
          mode="onChange"
          schema={addAssignmentUserSchema}
          onSubmit={onSubmit}
          formProps={{ shouldFocusError: false }}
        >
          <AssignmentUserForm />
          <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
            <CloseButton onClose={onClose} />
            <Button
              type="submit"
              color="blue"
              disabled={isAddingAssignmentUser}
              isLoading={isAddingAssignmentUser}
              enabledForDirty
            >
              Add User
            </Button>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
};

const AssignmentUserForm = () => {
  const { data: users, isLoading: isLoadingUsers } = useAllUsers();

  if (isLoadingUsers) {
    return <LoadingAddAssignmentUserForm />;
  }

  return (
    <div className="grid grid-cols-1 gap-6">
      <div className="flex flex-col gap-2">
        <Label htmlFor="userId">User</Label>
        <Select
          id="userId"
          name="userId"
          placeholder="Select User"
          options={
            users?.results?.map((user) => ({
              label: `${user.firstName} ${user.lastName}`,
              value: user.id,
            })) || []
          }
        />
      </div>
    </div>
  );
};

const LoadingAddAssignmentUserForm = () => {
  return (
    <div className="grid grid-cols-1 gap-6">
      <div className="flex flex-col gap-2">
        <Skeleton className="h-4 w-16" />
        <Skeleton className="h-10 w-full" />
      </div>
    </div>
  );
};
