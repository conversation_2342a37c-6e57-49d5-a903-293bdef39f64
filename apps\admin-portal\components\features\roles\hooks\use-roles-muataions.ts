import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

import { useOptimisticMutation } from "@/hooks/use-optimistic-mutation";
import {
  CreateRolePayload,
  PayloadAddPermissionToRole,
  Permission,
  ResponseRoleDetail,
  roles,
  UpdateRolePayload,
} from "@/lib/apis/roles";
import { capitalize } from "@/lib/utils";

import { roleKeys } from "./use-roles-queries";

export const useAddRole = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (payload: CreateRolePayload) => roles.addRole(payload),
    onError: (err) => toast.error(err?.message || "Something went wrong!"),
    onSuccess: () =>
      queryClient.invalidateQueries({
        queryKey: roleKeys.allRoleLists(),
      }),
    onSettled: (_, err) => !err && toast.success("Add Role Successfully!"),
  });
};

export const useArchiveRole = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: string) => roles.deleteRole(id),
    onError: (err) => toast.error(err?.message || "Fail to archive role!"),
    onSuccess: (_, payload) => {
      queryClient.invalidateQueries({
        queryKey: roleKeys.roleDetail(payload),
      });
      return queryClient.invalidateQueries({
        queryKey: roleKeys.allRoleLists(),
      });
    },
    onSettled: (_, err) => !err && toast.success("Archive role successfully!"),
  });
};

export const useUpdateRole = (id: string) => {
  return useMutation({
    mutationFn: (payload: UpdateRolePayload) => roles.updateRole(payload),
    onError: (err) => toast.error(err?.message || "Something went wrong!"),
    meta: {
      awaits: roleKeys.roleDetail(id),
      invalidates: roleKeys.allRoleLists(),
    },
    onSettled: (_, err) => !err && toast.success("Update Role Successfully!"),
  });
};

export const useAddPermission = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: PayloadAddPermissionToRole) =>
      roles.addPermission(payload),
    onError: (err) => toast.error(err?.message || "Something went wrong!"),
    onSuccess: (_, payload) =>
      queryClient.invalidateQueries({
        queryKey: roleKeys.roleDetail(payload.roleId),
      }),
    onSettled: (_, err) =>
      !err && toast.success("Add Permission Successfully!"),
  });
};

export const useRemovePermission = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: PayloadAddPermissionToRole) =>
      roles.deletePermission(payload),
    onError: (err) => toast.error(err?.message || "Something went wrong!"),
    onSuccess: (_, payload) =>
      queryClient.invalidateQueries({
        queryKey: roleKeys.roleDetail(payload.roleId),
      }),
    onSettled: (_, err) =>
      !err && toast.success("Delete Permission Successfully!"),
  });
};

// export const useOptimisticMutation = () => {
//   const queryClient = useQueryClient();

//   return useMutation({
//     mutationFn: (
//       payload: PayloadAddPermissionToRole & {
//         isAdd: boolean;
//         newPermission: Permission;
//       },
//     ) =>
//       payload.isAdd
//         ? roles.addPermission(payload)
//         : roles.deletePermission(payload),
//     onMutate: async (payload) => {
//       await queryClient.cancelQueries({
//         queryKey: roleKeys.roleDetail(payload.roleId),
//       });
//       toast.success(
//         `${payload.isAdd ? "Add" : "Remove"} permission successfully`,
//       );
//       const snapshot = queryClient.getQueryData(
//         roleKeys.roleDetail(payload.roleId),
//       );

//       queryClient.setQueryData<ResponseRoleDetail>(
//         roleKeys.roleDetail(payload.roleId),
//         (oldData) => {
//           if (!oldData) return undefined;

//           return {
//             ...oldData,
//             permissions: payload.isAdd
//               ? [payload.newPermission, ...oldData.permissions]
//               : oldData.permissions.filter(
//                   (p) => p.id !== payload.permissionId,
//                 ),
//           };
//         },
//       );

//       return () => {
//         queryClient.setQueryData(roleKeys.roleDetail(payload.roleId), snapshot);
//       };
//     },
//     onError: (err, payload, rollback) => {
//       const permissionAction = capitalize(
//         payload.newPermission.action.slice(0, -4),
//       );
//       const permissionSubject = capitalize(
//         payload.newPermission.permissionSubject.name,
//       );
//       toast.error(
//         err?.message ||
//           `Fail to ${payload.isAdd ? "add" : "remove"} ${permissionAction} to ${permissionSubject}`,
//       );
//       rollback?.();
//     },
//     onSettled: (_, err, payload) => {
//       return queryClient.invalidateQueries({
//         queryKey: roleKeys.roleDetail(payload.roleId),
//       });
//     },
//   });
// };

// export const useOptimisticAddPermission = () => {
//   const queryClient = useQueryClient();

//   return useMutation({
//     mutationFn: (
//       payload: PayloadAddPermissionToRole & {
//         isAdd: boolean;
//         newPermission: Permission;
//       },
//     ) => roles.addPermission(payload),
//     onMutate: async (payload) => {
//       await queryClient.cancelQueries({
//         queryKey: roleKeys.roleDetail(payload.roleId),
//       });
//       toast.success(`Add permission successfully`);
//       const snapshot = queryClient.getQueryData(
//         roleKeys.roleDetail(payload.roleId),
//       );

//       queryClient.setQueryData<ResponseRoleDetail>(
//         roleKeys.roleDetail(payload.roleId),
//         (oldData) => {
//           if (!oldData) return undefined;

//           return {
//             ...oldData,
//             permissions: [payload.newPermission, ...oldData.permissions],
//           };
//         },
//       );

//       return () => {
//         queryClient.setQueryData(roleKeys.roleDetail(payload.roleId), snapshot);
//       };
//     },
//     onError: (err, payload, rollback) => {
//       const permissionAction = capitalize(
//         payload.newPermission.action.slice(0, -4),
//       );
//       const permissionSubject = capitalize(
//         payload.newPermission.permissionSubject.name,
//       );
//       toast.error(
//         err?.message ||
//           `Fail to add ${permissionAction} to ${permissionSubject}`,
//       );
//       rollback?.();
//     },
//     onSettled: (_, __, payload) => {
//       return queryClient.invalidateQueries({
//         queryKey: roleKeys.roleDetail(payload.roleId),
//       });
//     },
//   });
// };

// export const useOptimisticRemovePermission = () => {
//   const queryClient = useQueryClient();

//   return useMutation({
//     mutationFn: (
//       payload: PayloadAddPermissionToRole & {
//         isAdd: boolean;
//         newPermission: Permission;
//       },
//     ) => roles.deletePermission(payload),
//     onMutate: async (payload) => {
//       await queryClient.cancelQueries({
//         queryKey: roleKeys.roleDetail(payload.roleId),
//       });
//       toast.success(`Remove permission successfully`);
//       const snapshot = queryClient.getQueryData(
//         roleKeys.roleDetail(payload.roleId),
//       );

//       queryClient.setQueryData<ResponseRoleDetail>(
//         roleKeys.roleDetail(payload.roleId),
//         (oldData) => {
//           if (!oldData) return undefined;
//           return {
//             ...oldData,
//             permissions: oldData.permissions.filter(
//               (p) => p.id !== payload.permissionId,
//             ),
//           };
//         },
//       );
//       return () => {
//         queryClient.setQueryData(roleKeys.roleDetail(payload.roleId), snapshot);
//       };
//     },
//     onError: (err, payload, rollback) => {
//       const permissionAction = capitalize(
//         payload.newPermission.action.slice(0, -4),
//       );
//       const permissionSubject = capitalize(
//         payload.newPermission.permissionSubject.name,
//       );
//       toast.error(
//         err?.message ||
//           `Fail to remove ${permissionAction} to ${permissionSubject}`,
//       );
//       rollback?.();
//     },
//     onSettled: (_, err, payload) => {
//       return queryClient.invalidateQueries({
//         queryKey: roleKeys.roleDetail(payload.roleId),
//       });
//     },
//   });
// };

export const useTogglePermission = (roleId: string) => {
  return useOptimisticMutation({
    invalidates: roleKeys.roleDetail(roleId),
    queryKey: roleKeys.roleDetail(roleId),
    mutationKey: ["optimistic-toggle-permission"],
    mutationFn: (
      payload: PayloadAddPermissionToRole & {
        isAdd: boolean;
        newPermission: Permission;
      },
    ) =>
      payload.isAdd
        ? roles.addPermission(payload)
        : roles.deletePermission(payload),
    updater: (data: ResponseRoleDetail | undefined, payload) => {
      if (!data) return undefined;

      return {
        ...data,
        permissions: payload.isAdd
          ? [payload.newPermission, ...data.permissions]
          : data.permissions.filter((p) => p.id !== payload.permissionId),
      };
    },
    onMutate: (payload) => {
      toast.success(
        `${payload.isAdd ? "Add" : "Remove"} permission successfully`,
      );
    },
    onError: (err, payload) => {
      if ("message" in err) {
        toast.error(err.message);
      } else {
        const permissionAction = capitalize(
          // ignore 4 last letter (":own")
          payload.newPermission.action.slice(0, -4),
        );
        const permissionSubject = capitalize(
          payload.newPermission.permissionSubject.name,
        );
        toast.error(
          `Fail to ${payload.isAdd ? "add" : "remove"} ${permissionAction} to ${permissionSubject}`,
        );
      }
    },
  });
};
