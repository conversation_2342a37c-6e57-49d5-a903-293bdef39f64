import BaseApi from "../base";
import { MetadataParams } from "../types";
import type {
  AutoCategorizationFilters,
  CategorizationMethodDetail,
  CategorizationMethodsResponse,
  CategorizationMethodStats,
  CategorizationPerformanceResponse,
  MethodTypeDistributionResponse,
} from "./types";

export * from "./types";

class AutoCategorizationApi extends BaseApi {
  constructor() {
    super("/auto-categorization", true);
  }

  public async getCategorizationMethods(
    params: AutoCategorizationFilters & MetadataParams = {},
  ) {
    return this.http.get<CategorizationMethodsResponse>("/methods", params);
  }

  public async getCategorizationMethodStats(
    params: AutoCategorizationFilters = {},
  ) {
    return this.http.get<CategorizationMethodStats>("/stats/methods", params);
  }

  public async getCategorizationPerformance(
    params: AutoCategorizationFilters = {},
  ) {
    return this.http.get<CategorizationPerformanceResponse>(
      "/stats/performance",
      params,
    );
  }

  public async getMethodTypeDistribution(
    params: AutoCategorizationFilters = {},
  ) {
    return this.http.get<MethodTypeDistributionResponse>(
      "/stats/method-types",
      params,
    );
  }

  public async getCategorizationMethodDetail(
    id: string,
    params: AutoCategorizationFilters = {},
  ) {
    return this.http.get<CategorizationMethodDetail>(`/methods/${id}`, params);
  }
}

export const autoCategorizationApi = new AutoCategorizationApi();
