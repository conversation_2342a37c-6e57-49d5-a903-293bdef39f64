import BaseApi from "../base";
import type { MetadataParams } from "../types";
import type {
  CategorizationMethod,
  CategorizationMethodsResponse,
} from "./types";

export * from "./types";

// Dummy data for development
const generateDummyMethods = (
  page: number = 1,
  take: number = 50,
): CategorizationMethodsResponse => {
  const totalCount = 127; // Total dummy records
  const startIndex = (page - 1) * take;

  const dummyMethods: CategorizationMethod[] = [];

  for (let i = 0; i < take && startIndex + i < totalCount; i++) {
    const index = startIndex + i + 1;
    const types = ["ai", "rule-based", "hybrid"] as const;
    const statuses = ["active", "inactive", "draft"] as const;

    dummyMethods.push({
      id: `method-${index}`,
      name: `Categorization Method ${index}`,
      description: `Advanced ${types[index % 3]} categorization method for document classification`,
      type: types[index % 3],
      status: statuses[index % 3],
      accuracy: 0.75 + Math.random() * 0.24, // 75-99% accuracy
      totalCategorizations: Math.floor(Math.random() * 10000) + 100,
      successfulCategorizations: Math.floor(Math.random() * 9000) + 90,
      failedCategorizations: Math.floor(Math.random() * 100) + 10,
      averageConfidence: 0.7 + Math.random() * 0.29, // 70-99% confidence
      version: `${Math.floor(index / 10) + 1}.${index % 10}`,
      isDefault: index === 1,
      createdDate: new Date(
        Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000,
      ).toISOString(),
      lastUpdatedDate: new Date(
        Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000,
      ).toISOString(),
      createdBy: `user-${Math.floor(Math.random() * 10) + 1}`,
      lastUsedDate:
        Math.random() > 0.2
          ? new Date(
              Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000,
            ).toISOString()
          : undefined,
    });
  }

  return {
    results: dummyMethods,
    count: dummyMethods.length,
    metadata: {
      totalCount,
      itemCount: dummyMethods.length,
      itemsPerPage: take,
      totalPages: Math.ceil(totalCount / take),
      currentPage: page,
    },
  };
};

class AutoCategorizationApi extends BaseApi {
  constructor() {
    super("/auto-categorization", true);
  }

  public async getCategorizationMethods(params: MetadataParams = {}) {
    // Return dummy data for development
    // TODO: Replace with actual API call when backend is ready
    const { page = 1, take = 50 } = params;
    return Promise.resolve(generateDummyMethods(page, take));

    // Uncomment when real API is available:
    // return this.http.get<CategorizationMethodsResponse>("/methods", params);
  }
}

export const autoCategorizationApi = new AutoCategorizationApi();
