import { endOfDay, startOfDay, subDays } from "date-fns";
import { DateRange } from "react-day-picker";

export type DateOptions =
  | "today"
  | "yesterday"
  | "last_week"
  | "last_month"
  | "last_quarter"
  | "last_year"
  | "custom"
  | "";

export const DAYS = {
  today: 0,
  yesterday: 1,
  last_week: 7,
  last_month: 30,
  last_quarter: 90,
  last_year: 365,
} as const;

export const calculateDateRange = (
  option: DateOptions | "",
  customRange?: DateRange,
) => {
  const now = new Date();

  if (!option || option === "today") {
    return {
      fromDate: startOfDay(now).toISOString(),
      toDate: endOfDay(now).toISOString(),
    };
  }

  if (option === "yesterday") {
    const yesterday = subDays(now, 1);
    return {
      fromDate: startOfDay(yesterday).toISOString(),
      toDate: endOfDay(yesterday).toISOString(),
    };
  }

  if (option && option !== "custom") {
    const days = DAYS[option];
    const to = subDays(now, days);
    return {
      fromDate: startOfDay(to).toISOString(),
      toDate: endOfDay(now).toISOString(),
    };
  }

  if (option === "custom") {
    return {
      fromDate: customRange?.from
        ? startOfDay(customRange.from).toISOString()
        : undefined,
      toDate: customRange?.to
        ? endOfDay(customRange.to).toISOString()
        : undefined,
    };
  }
};

export const snakeCaseToCapitalize = (text: string): string => {
  if (!text || typeof text !== "string") {
    return "";
  }

  return text
    .split("_")
    .map((word) => {
      if (word.length === 0) return "";
      return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
    })
    .join(" ");
};
