"use client";

import { Card } from "flowbite-react";
import { <PERSON>User<PERSON><PERSON>ck, FiUserP<PERSON>, FiUsers, FiUserX } from "react-icons/fi";

import { Skeleton } from "@/components/ui/skeleton";
import { createRandomArray } from "@/lib/utils";

import { useUserStatistic } from "./hooks/use-user-management-queries";

export const UserStatistics = () => {
  const { data: userStatistic, isPending: isPendingUserStatistic } =
    useUserStatistic();

  if (isPendingUserStatistic) {
    return <UserStatsSkeleton />;
  }

  return (
    <div>
      <h3 className="mb-4 text-lg font-medium text-gray-900 dark:text-white">
        User Statistics
      </h3>
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 xl:grid-cols-4">
        <Card className="[&>div]:p-6">
          <div className="flex items-center gap-4">
            <div className="flex size-14 flex-shrink-0 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/40">
              <FiUsers className="size-7 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="flex-1">
              <span className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {userStatistic?.totalUsers}
              </span>
              <span className="mt-1 block text-base font-medium text-gray-900 dark:text-white">
                Total Users
              </span>
            </div>
          </div>
        </Card>
        <Card className="[&>div]:p-6">
          <div className="flex items-center gap-4">
            <div className="flex size-14 flex-shrink-0 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/40">
              <FiUserCheck className="size-7 text-green-600 dark:text-green-400" />
            </div>
            <div className="flex-1">
              <span className="text-2xl font-bold text-green-600 dark:text-green-400">
                {userStatistic?.activeUsers}
              </span>
              <span className="mt-1 block text-base font-medium text-gray-900 dark:text-white">
                Active Users
              </span>
            </div>
          </div>
        </Card>
        <Card className="[&>div]:p-6">
          <div className="flex items-center gap-4">
            <div className="flex size-14 flex-shrink-0 items-center justify-center rounded-full bg-purple-100 dark:bg-purple-900/40">
              <FiUserPlus className="size-7 text-purple-600 dark:text-purple-400" />
            </div>
            <div className="flex-1">
              <span className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                {userStatistic?.newUsers}
              </span>
              <span className="mt-1 block text-base font-medium text-gray-900 dark:text-white">
                New Users
              </span>
            </div>
          </div>
        </Card>
        <Card className="[&>div]:p-6">
          <div className="flex items-center gap-4">
            <div className="flex size-14 flex-shrink-0 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/40">
              <FiUserX className="size-7 text-red-600 dark:text-red-400" />
            </div>
            <div className="flex-1">
              <span className="text-2xl font-bold text-red-600 dark:text-red-400">
                {userStatistic?.lockedUsers}
              </span>
              <span className="mt-1 block text-base font-medium text-gray-900 dark:text-white">
                Locked Accounts
              </span>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

const UserStatsSkeleton = () => (
  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 xl:grid-cols-4">
    {createRandomArray(4).map((index) => (
      <Card key={index} className="[&>div]:p-6">
        <div className="flex items-center gap-4">
          <Skeleton className="size-14 flex-shrink-0 rounded-full" />
          <div className="flex-1">
            <Skeleton className="mb-1 h-[29px] w-16" />
            <Skeleton className="h-[24px] w-24" />
          </div>
        </div>
      </Card>
    ))}
  </div>
);
