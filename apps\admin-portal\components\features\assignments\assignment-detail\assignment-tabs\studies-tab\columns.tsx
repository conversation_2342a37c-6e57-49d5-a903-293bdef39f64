import type { ColumnDef } from "@tanstack/react-table";

import { Button } from "@/components/ui/button";
import type { AssignmentStudy } from "@/lib/apis/assignments";

export const columns: ColumnDef<AssignmentStudy>[] = [
  {
    header: "Name",
    accessorFn: (row) => row.study.name,
  },
  {
    header: "NCT",
    accessorFn: (row) => row.study.nct,
  },
  {
    header: "Study Code",
    accessorFn: (row) => row.study.studyCode,
  },
  {
    header: "CRO",
    accessorFn: (row) => row.study.cro,
  },
  {
    header: "Phase",
    accessorFn: (row) => row.study.phase,
  },
  {
    id: "actions",
    header: "Actions",
    cell: () => (
      <div className="flex gap-2">
        <Button variant="outline" size="sm" onClick={() => {}}>
          Remove
        </Button>
        <Button variant="primary" size="sm" onClick={() => {}}>
          Add to Assignment
        </Button>
      </div>
    ),
  },
];
