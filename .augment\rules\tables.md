---
type: "agent_requested"
description: "Example description"
---

# Table Implementation Standards

This document establishes standardized patterns for table implementation across the admin-portal application, based on the training modules table as the primary reference implementation.

## Core Libraries

- **TanStack Table (@tanstack/react-table)** - Used for table state management, sorting, and column definitions
- **Flowbite React** - Base table UI components and styling
- **React Query** - Data fetching and loading state management
- **nuqs** - URL state management for sorting, pagination, and filtering

## Table Architecture Standards

### Basic Table Structure

All tables should follow this standardized structure using the `Table` component with separate column definitions:

```typescript
// components/features/training/tabs/modules/index.tsx
import { Table } from "@/components/ui/table";
import { TableLoading } from "@/components/ui/table/table-loading";
import LoadingWrapper from "@/components/shared/loading-wrapper";
import { columns } from "./columns";

export const ModulesTab = () => {
  const { data, isPending, isPlaceholderData } = useModules();
  const { orderBy, orderDirection, changeSort } = useSort();

  const sorting = !orderBy
    ? []
    : [{ id: orderBy, desc: orderDirection === "desc" }];

  return (
    <Card className="[&>div]:p-0">
      <div className="mb-4 flex items-center justify-between p-4 pb-0 text-lg font-semibold">
        <div className="dark:text-gray-400">Modules</div>
        <Button variant="primary" onClick={openModal}>
          <IoMdAdd />
          Add Module
        </Button>
      </div>

      {isPending ? (
        <TableLoading columns={columns} />
      ) : (
        <>
          <LoadingWrapper isLoading={isPlaceholderData}>
            <Table
              columns={columns}
              enableSorting //optional
              data={data?.results ?? []}
            />
          </LoadingWrapper>
          {data?.metadata && <TableDataPagination metadata={data.metadata} />}
        </>
      )}
    </Card>
  );
};
```

### Column Definition Structure

Column definitions should be defined in separate files using TanStack Table's `ColumnDef`:

```typescript
// components/features/training/tabs/modules/columns.tsx
import { ColumnDef } from "@tanstack/react-table";
import { TableEditButton, TableRemoveButton } from "@/components/shared/table-action-buttons";
import { PillBadge } from "@/components/ui/badges/pill-badge";
import { Module } from "@/lib/apis/training-modules";

export const columns: ColumnDef<Module>[] = [
  {
    accessorKey: "name",
    header: "Name",
    cell: ({ row }) => (
      <div className="font-medium">{row.getValue("name")}</div>
    ),
  },
  {
    accessorKey: "description",
    header: "Description",
    cell: ({ row }) => (
      <div className="max-w-xs truncate" title={row.getValue("description")}>
        {row.getValue("description") || "—"}
      </div>
    ),
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const status = row.getValue("status") as string;
      return (
        <PillBadge variant={status === "active" ? "success" : "default"}>
          {status}
        </PillBadge>
      );
    },
  },
  {
    accessorKey: "createdDate",
    header: "Created Date",
    cell: ({ row }) => {
      const date = row.getValue("createdDate") as string;
      return new Date(date).toLocaleDateString();
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => (
      <div className="flex gap-2">
        <TableEditButton
          type="button"
          onClick={() => handleEdit(row.original)}
        />
        <TableRemoveButton
          onClick={() => handleDelete(row.original.id)}
        />
      </div>
    ),
  },
];
```

## Table Component Props

The `Table` component accepts these key props:

```typescript
type TableDataProps<TData, TValue> = {
  columns: ColumnDef<TData, TValue>[]; // Column definitions
  data: TData[]; // Table data
  enableSorting?: boolean; // Enable column sorting
  isLoading?: boolean; // Show skeleton loading
  sorting?: SortingState; // External sorting state
  onSortingChange?: (
    updater: SortingState | ((old: SortingState) => SortingState),
  ) => void;
  manualSorting?: boolean; // Use manual sorting (default: true)
  headCellStyle?: string; // Custom header cell styling
  draggable?: TableDragAbleProps; // Enable drag and drop
  collapsible?: CollapsibleRowConfig<TData>; // Enable row expansion
};
```

## Loading State Management

### Primary Loading State (isPending)

Use `isPending` from React Query for initial loading with skeleton:

```typescript
const { data, isPending, isPlaceholderData } = useModules();

return (
  <>
    {isPending ? (
      <TableLoading columns={columns} length={4} />
    ) : (
      // Table content
    )}
  </>
);
```

### Secondary Loading State (isPlaceholderData) (only apply when query contains params)

Use `isPlaceholderData` for refetch loading with overlay:

```typescript
<LoadingWrapper isLoading={isPlaceholderData}>
  <Table
    columns={columns}
    data={data?.results ?? []}
    enableSorting
  />
</LoadingWrapper>
```

### TableLoading Component

The `TableLoading` component creates skeleton rows:

```typescript
import { TableLoading } from "@/components/ui/table/table-loading";

<TableLoading
  columns={columns}    // Same column definitions as main table
/>
```

### LoadingWrapper Component

The `LoadingWrapper` component adds overlay loading:

```typescript
import LoadingWrapper from "@/components/shared/loading-wrapper";

<LoadingWrapper isLoading={isPlaceholderData}>
  {/* Table content */}
</LoadingWrapper>
```

## Reusable Table Components

### Table Action Buttons

Standard action buttons for table rows with consistent styling:

```typescript
import {
  TableEditButton,
  TableViewButton,
  TableRemoveButton,
  TableGenericButton
} from "@/components/shared/table-action-buttons";

// Edit button - supports both button and link modes
<TableEditButton
  type="button"
  onClick={() => handleEdit(row.original)}
  disabled={isLoading}
/>

<TableEditButton
  type="link"
  href={`/modules/${row.original.id}/edit`}
/>

// View button
<TableViewButton
  type="button"
  onClick={() => handleView(row.original)}
/>

<TableViewButton
  type="link"
  href={`/modules/${row.original.id}`}
/>

// Remove/Delete button
<TableRemoveButton
  onClick={() => handleDelete(row.original.id)}
  label="Delete"        // Optional: default is "Delete"
  disabled={isDeleting}
/>

// Generic button for custom actions
<TableGenericButton
  type="button"
  onClick={() => handleCustomAction(row.original)}
>
  <FiDownload className="h-4 w-4" />
  Download
</TableGenericButton>
```

**Action Button Props:**

- `type: "button" | "link"` - Render as button or Next.js Link
- `onClick?: () => void` - Click handler (button type only)
- `href?: string` - Navigation URL (link type only)
- `disabled?: boolean` - Disable button (button type only)
- `className?: string` - Additional CSS classes
- `children?: React.ReactNode` - Custom content (generic button only)

### Pagination Component

Standard pagination with metadata display:

```typescript
import { TableDataPagination } from "@/components/ui/pagination";

<TableDataPagination
  metadata={data.metadata}  // MetadataResponse from API
/>

// With external state management
<TableDataPagination
  isUseExternalState={true}
  metadata={data.metadata}
  page={currentPage}
  setPage={setCurrentPage}
/>
```

**Pagination Features:**

- Automatic "Showing X to Y of Z" display
- Responsive design (collapses on mobile)
- Integration with `usePagination` hook for URL state
- Support for external state management
- Automatic hiding when no data

## Column Definition Patterns

### Basic Data Columns

```typescript
// Simple text column
{
  accessorKey: "name",
  header: "Name",
}

// Formatted text column
{
  accessorKey: "name",
  header: "Name",
  cell: ({ row }) => (
    <div className="font-medium">{row.getValue("name")}</div>
  ),
}

// Nested object access
{
  accessorKey: "address.city",
  header: "City",
}

{
  accessorKey: "address.stateProvince.name",
  header: "State / Province",
}
```

### Date Formatting Columns

```typescript
{
  accessorKey: "createdDate",
  header: "Created Date",
  cell: ({ row }) => row.original.createdDate ? formatDate(row.original.createdDate) : "-",
}

```

### Link Columns

```typescript
// Link to detail page
{
  accessorKey: "id",
  header: "Study ID",
  cell: ({ row }) => (
    <Link
      className="text-primary-500 cursor-pointer whitespace-nowrap underline-offset-4 hover:underline"
      href={`/studies/${row.original.id}`}
    >
      {row.original.id}
    </Link>
  ),
}

// Link with icon
{
  id: "view",
  header: "View",
  cell: ({ row }) => (
    <Link
      href={`/sites/${row.original.id}`}
      className="text-primary-500 hover:text-primary-600"
    >
      <IoMdEye className="h-4 w-4" />
    </Link>
  ),
}
```

### Truncated Text Columns

```typescript
{
  accessorKey: "description",
  header: "Description",
  cell: ({ row }) => {
    const description = row.getValue("description") as string;
    return (
      <div
        className="max-w-xs truncate"
        title={description}
      >
        {description || "—"}
      </div>
    );
  },
}
```

### Actions Column

```typescript
{
  id: "actions",
  header: "Actions",
  cell: ({ row }) => (
    <div className="flex gap-2">
      <TableEditButton
        type="button"
        onClick={() => handleEdit(row.original)}
      />
      <TableRemoveButton
        onClick={() => handleDelete(row.original.id)}
        disabled={isDeleting}
      />
    </div>
  ),
}
```

## Sorting Integration

### URL-Based Sorting

Integrate with `useSort` hook for URL-synchronized sorting:

```typescript
import { useSort } from "@/hooks/use-sort";
import { functionalUpdate } from "@tanstack/react-table";

export const MyTableComponent = () => {
  const { orderBy, orderDirection, changeSort } = useSort();

  const sorting = !orderBy
    ? []
    : [{ id: orderBy, desc: orderDirection === "desc" }];

  return (
    <Table
      columns={columns}
      data={data?.results ?? []}
      enableSorting
      sorting={sorting}
      onSortingChange={(updater) => {
        const newSorting = functionalUpdate(updater, sorting);
        const sort = newSorting[0];
        if (sort) return changeSort(sort.id, sort.desc ? "desc" : "asc");
        changeSort(); // Clear sorting
      }}
    />
  );
};
```

### Manual vs Automatic Sorting

```typescript
// Manual sorting (default) - sorting handled by server
<Table
  columns={columns}
  data={data}
  enableSorting
  manualSorting={true}  // Default
  sorting={sorting}
  onSortingChange={handleSortingChange}
/>

// Automatic sorting - sorting handled by TanStack Table
<Table
  columns={columns}
  data={data}
  enableSorting
  manualSorting={false}
/>
```

## Data Fetching Integration

### Standard Query Hook Integration

```typescript
export const MyTableComponent = () => {
  const { data, isPending, isPlaceholderData } = useMyData();

  return (
    <>
      {isPending ? (
        <TableLoading columns={columns} />
      ) : (
        <>
          <LoadingWrapper isLoading={isPlaceholderData}>
            <Table
              columns={columns}
              data={data?.results ?? []}
              enableSorting
            />
          </LoadingWrapper>
          {data?.metadata && (
            <TableDataPagination metadata={data.metadata} />
          )}
        </>
      )}
    </>
  );
};
```

### Query Hook with Filters

```typescript
// Hook that consumes URL state internally
const useMyData = () => {
  const { page, take } = usePagination();
  const { search } = useSearch();
  const { orderBy, orderDirection } = useSort();

  const params = {
    page,
    take,
    orderBy: orderBy || undefined,
    orderDirection: orderDirection || undefined,
    filter: { name: search },
  };

  return useQuery({
    queryKey: ["my-data", params],
    queryFn: () => api.myData.list(params),
    placeholderData: (prev) => prev,
  });
};
```

## Empty States and Error Handling

### Empty State Handling

```typescript
{isPending ? (
  <TableLoading columns={columns} />
) : data?.results?.length === 0 ? (
  <div className="flex flex-col items-center justify-center py-12">
    <div className="text-gray-500 dark:text-gray-400">
      No data found
    </div>
  </div>
) : (
  <Table
    columns={columns}
    data={data?.results ?? []}
    enableSorting
  />
)}
```

## Naming Conventions

### File Organization

```
components/features/{feature-name}/
├── index.tsx              # Main table component
├── columns.tsx            # Column definitions
└── hooks/
    └── use-{feature}.ts   # Data fetching hook
```

### Column ID Conventions

```typescript
// Use accessorKey for data columns
{ accessorKey: "name", header: "Name" }
{ accessorKey: "createdDate", header: "Created Date" }

// Use id for computed/action columns
{ id: "actions", header: "Actions" }
{ id: "status", header: "Status" }
{ id: "fullName", header: "Full Name" }
```

## Best Practices Summary

### Table Architecture

1. **Separate column definitions** - Keep columns in separate files for reusability
2. **Use proper loading states** - Implement both skeleton and overlay loading
3. **Integrate with URL state** - Use hooks for sorting, pagination, and filtering
4. **Handle empty and error states** - Provide meaningful feedback to users

### Performance Optimization

1. **Use placeholderData** - Keep previous data during refetches for smooth UX
2. **Implement proper memoization** - Memoize column definitions and callbacks
3. **Optimize re-renders** - Use stable references for props and handlers
4. **Consider virtualization** - For very large datasets, implement table virtualization

### User Experience

1. **Consistent action buttons** - Use standardized action button components
2. **Responsive design** - Ensure tables work well on all screen sizes
3. **Proper accessibility** - Include ARIA labels and keyboard navigation
4. **Clear visual hierarchy** - Use consistent styling and spacing

### Code Organization

1. **Follow naming conventions** - Use consistent naming for files and components
2. **Co-locate related code** - Keep table components and columns together
3. **Reuse common patterns** - Extract reusable column definitions and components
4. **Document complex logic** - Add comments for complex column calculations

This documentation serves as the definitive guide for implementing consistent, performant, and accessible tables across the admin-portal application.
