import { useFormContext } from "react-hook-form";
import { z } from "zod";

import { <PERSON><PERSON>, CloseButton } from "@/components/ui/button";
import { Form, Select, Textarea } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { Modal } from "@/components/ui/modal";
import { Skeleton } from "@/components/ui/skeleton";
import { useGroups } from "@/hooks/use-groups";
import { useAllSites } from "@/hooks/use-sites";

import { useAddAssignment } from "../hooks/use-add-assignment";

export const addAssignmentSchema = z
  .object({
    groupId: z
      .string({ required_error: "Please select a group" })
      .min(1, "Please select a group"),
    description: z
      .string({ required_error: "Please enter a description" })
      .min(1, "Please enter a description"),
    type: z.enum(["site", "cro", "clincove"]),
    siteId: z
      .string({ required_error: "Please select a site" })
      .min(1, "Please select a site"),
  })
  .refine((data) => !(data.type === "site" && !data.siteId), {
    message: "siteId is required when type is 'site'",
    path: ["siteId"],
  });

type ModalAddAssignmentProps = {
  isOpen: boolean;
  onClose: () => void;
};

export const ModalAddAssignment = function ({
  isOpen,
  onClose,
}: ModalAddAssignmentProps) {
  const { mutateAsync: addAssignment, isPending: isAddingAssignment } =
    useAddAssignment();

  async function onSubmit(data: z.infer<typeof addAssignmentSchema>) {
    await addAssignment(data);
    onClose();
  }

  return (
    <Modal show={isOpen} onClose={onClose} className="[&>div]:max-w-2xl">
      <Modal.Header>Add Assignment</Modal.Header>
      <Modal.Body>
        <Form
          mode="onChange"
          schema={addAssignmentSchema}
          onSubmit={onSubmit}
          formProps={{ shouldFocusError: false }}
        >
          <AssignmentForm />
          <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
            <CloseButton onClose={onClose} />
            <Button
              type="submit"
              color="blue"
              disabled={isAddingAssignment}
              isLoading={isAddingAssignment}
            >
              Add Assignment
            </Button>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
};

type AssignmentFormProps = {
  hiddenGroup?: boolean;
};

export const AssignmentForm = ({ hiddenGroup }: AssignmentFormProps) => {
  const { watch } = useFormContext();
  const { data: groups, isLoading: isLoadingGroups } = useGroups();
  const { data: sites, isLoading: isLoadingSites } = useAllSites();

  if (isLoadingGroups || isLoadingSites) {
    return <LoadingAddAssignmentForm />;
  }

  return (
    <div className="grid grid-cols-1 gap-6">
      {!hiddenGroup && (
        <div className="flex flex-col gap-2">
          <Label htmlFor="groupId">Group</Label>
          <Select
            id="groupId"
            name="groupId"
            placeholder="Select Group"
            options={
              groups?.results?.map((group) => ({
                label: group.name,
                value: group.id,
              })) || []
            }
          />
        </div>
      )}

      <div className="flex flex-col gap-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          name="description"
          placeholder="Enter description..."
        />
      </div>

      <div className="flex flex-col gap-2">
        <Label htmlFor="type">Type</Label>
        <Select
          id="type"
          name="type"
          placeholder="Select Type"
          options={[
            { label: "Site", value: "site" },
            { label: "CRO", value: "cro" },
            { label: "Clincove", value: "clincove" },
          ]}
        />
      </div>

      <div
        className="flex flex-col gap-2"
        style={{ display: watch("type") === "site" ? "block" : "none" }}
      >
        <Label htmlFor="siteId">Site</Label>
        <Select
          id="siteId"
          name="siteId"
          placeholder="Select Site"
          options={
            sites?.results?.map((site) => ({
              label: site.name,
              value: site.id,
            })) || []
          }
        />
      </div>
    </div>
  );
};

const LoadingAddAssignmentForm = () => {
  return (
    <div className="grid grid-cols-1 gap-6">
      <div className="flex flex-col gap-2">
        <Skeleton className="h-4 w-16" />
        <Skeleton className="h-10 w-full" />
      </div>

      <div className="flex flex-col gap-2">
        <Skeleton className="h-4 w-24" />
        <Skeleton className="h-24 w-full" />
      </div>

      <div className="flex flex-col gap-2">
        <Skeleton className="h-4 w-12" />
        <Skeleton className="h-10 w-full" />
      </div>

      <div className="flex flex-col gap-2">
        <Skeleton className="h-4 w-12" />
        <Skeleton className="h-10 w-full" />
      </div>
    </div>
  );
};
