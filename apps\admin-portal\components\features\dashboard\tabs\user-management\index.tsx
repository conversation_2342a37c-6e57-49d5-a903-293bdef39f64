"use client";

import { LoginActivity } from "./login-activity";
import { RoleDistribution } from "./role-distribution";
import { UserStatistics } from "./user-statistics";
import { UsersWithoutKeyInfo } from "./users-without-key-info";

export const UserManagementTab = () => {
  return (
    <div className="space-y-8">
      <h2 className="text-xl font-bold text-gray-900 sm:text-2xl dark:text-white">
        User & Access Management
      </h2>

      <UserStatistics />
      <LoginActivity />
      <RoleDistribution />
      <UsersWithoutKeyInfo />
    </div>
  );
};
