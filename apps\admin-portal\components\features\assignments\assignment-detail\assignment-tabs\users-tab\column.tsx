import type { ColumnDef } from "@tanstack/react-table";
import { MdCheckBox, MdDelete } from "react-icons/md";

import { UserStatusBadge } from "@/components/ui/badges/user-status-badge";
import type { AssignmentUser } from "@/lib/apis/assignments";

export const generateUserColumns = (onRemove: (userId: string) => void) => {
  const columns: ColumnDef<AssignmentUser>[] = [
    {
      header: "Name",
      accessorFn: (row) => `${row.user.firstName} ${row.user.lastName}`,
    },
    {
      header: "Email",
      accessorFn: (row) => row.user.email,
    },
    {
      header: "Active",
      accessorFn: (row) => (row.user.isActive ? "Yes" : "No"),
      cell: ({ row }) => {
        if (row.original.user.isActive) {
          return <MdCheckBox className="size-5 text-green-500" />;
        }
        return null;
      },
    },
    {
      header: "Status",
      accessorFn: (row) => row.user.status,
      cell: ({ row }) => <UserStatusBadge status={row.original.user.status} />,
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <div className="flex gap-2">
          <button
            className="flex items-center gap-1 text-red-500 hover:text-red-600"
            onClick={() => onRemove(row.original.user.id)}
          >
            <MdDelete className="h-4 w-4" />
            Remove
          </button>
        </div>
      ),
    },
  ];

  return columns;
};
