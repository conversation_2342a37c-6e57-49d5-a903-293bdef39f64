import { ListBaseResponse } from "../types";

export type AutoCategorizationFilters = {
  sponsorId?: string;
  studyId?: string;
  fromDate?: string;
  toDate?: string;
  methodType?: string;
  status?: string;
};

export type CategorizationMethod = {
  id: string;
  name: string;
  description: string;
  methodType: "ai" | "rule-based" | "hybrid";
  status: "active" | "inactive" | "draft";
  accuracy: number;
  totalCategorizations: number;
  successfulCategorizations: number;
  failedCategorizations: number;
  averageConfidence: number;
  createdDate: string;
  lastUpdatedDate: string;
  createdBy: string;
  lastUsedDate?: string;
  version: string;
  isDefault: boolean;
};

export type CategorizationMethodsResponse = ListBaseResponse<CategorizationMethod>;

export type CategorizationMethodStats = {
  totalMethods: number;
  activeMethods: number;
  averageAccuracy: number;
  totalCategorizations: number;
};

export type CategorizationPerformanceData = {
  date: string;
  successful: number;
  failed: number;
  accuracy: number;
};

export type CategorizationPerformanceResponse = CategorizationPerformanceData[];

export type MethodTypeDistribution = {
  type: "ai" | "rule-based" | "hybrid";
  count: number;
  percentage: number;
};

export type MethodTypeDistributionResponse = MethodTypeDistribution[];

export type CategorizationMethodDetail = CategorizationMethod & {
  rules?: string[];
  aiModelInfo?: {
    modelName: string;
    version: string;
    trainingDate: string;
  };
  recentCategorizations: {
    id: string;
    filename: string;
    result: string;
    confidence: number;
    timestamp: string;
    success: boolean;
  }[];
};
