import type { ListBaseResponse } from "../types";

export type CategorizationMethod = {
  id: string;
  name: string;
  description: string;
  type: "ai" | "rule-based" | "hybrid";
  status: "active" | "inactive" | "draft";
  accuracy: number;
  totalCategorizations: number;
  successfulCategorizations: number;
  failedCategorizations: number;
  averageConfidence: number;
  version: string;
  isDefault: boolean;
  createdDate: string;
  lastUpdatedDate: string;
  createdBy: string;
  lastUsedDate?: string;
};

export type CategorizationMethodsResponse = ListBaseResponse<CategorizationMethod>;
