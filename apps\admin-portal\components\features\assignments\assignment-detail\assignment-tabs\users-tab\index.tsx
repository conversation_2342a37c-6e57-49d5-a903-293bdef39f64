import { useQueryClient } from "@tanstack/react-query";
import { Card } from "flowbite-react";
import { useParams } from "next/navigation";
import { useMemo, useState } from "react";
import { IoMdAdd } from "react-icons/io";

import { useRemoveUserAssignment } from "@/components/features/users/hooks/use-remove-user-assignment";
import { Button } from "@/components/ui/button";
import { Table } from "@/components/ui/table";

import {
  USE_ASSIGNMENT_USERS_QUERY_KEY,
  useAssignmentUsers,
} from "../../../hooks/use-assignment-users";
import { generateUserColumns } from "./column";
import { ModalAddAssignmentUser } from "./modal-add-user";

export const UsersTab = () => {
  const params = useParams();
  const queryClient = useQueryClient();
  const { data, isLoading } = useAssignmentUsers(params.id as string);
  const { mutateAsync: removeUserAssignment } = useRemoveUserAssignment();
  const [isOpen, setIsOpen] = useState(false);

  const columns = useMemo(
    () =>
      generateUserColumns(async (userId) => {
        await removeUserAssignment({
          id: userId,
          assignmentId: params.id as string,
        });
        queryClient.invalidateQueries({
          queryKey: [USE_ASSIGNMENT_USERS_QUERY_KEY, params.id],
        });
      }),
    [removeUserAssignment, params.id, queryClient],
  );

  return (
    <>
      <Card className="[&>div]:p-0">
        <div className="flex items-center justify-between p-4">
          <div className="text-lg font-semibold">Users</div>
          <Button variant="primary" onClick={() => setIsOpen(true)}>
            <IoMdAdd />
            Add User
          </Button>
        </div>
        {isLoading ? (
          <Table
            columns={columns}
            data={Array.from({ length: 5 }).map(
              (_, idx) => ({ id: idx }) as any,
            )}
            isLoading
          />
        ) : (
          <Table columns={columns} data={data?.results ?? []} />
        )}
      </Card>
      <ModalAddAssignmentUser
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
      />
    </>
  );
};
