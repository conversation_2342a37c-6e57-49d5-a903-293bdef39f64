"use client";
import { TabsWrapper } from "@/components/ui/tabs-wrapper";

import { ActionsTab, SubjectsTab } from "./tabs";

const PERMISSION_TABS = [
  {
    title: "Permission Subjects",
    key: "subject",
    content: <SubjectsTab />,
  },
  {
    title: "Permission",
    key: "actions",
    content: <ActionsTab />,
  },
];

export const PermissionsContent = () => {
  return (
    <>
      <h1 className="text-2xl font-semibold text-gray-900 sm:text-2xl dark:text-white">
        Permissions
      </h1>
      <TabsWrapper tabs={PERMISSION_TABS} />
    </>
  );
};
