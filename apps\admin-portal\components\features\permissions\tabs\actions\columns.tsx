import { type ColumnDef } from "@tanstack/react-table";
import { capitalize } from "lodash";

import { TableViewButton } from "@/components/shared/table-action-buttons";
import { Permission } from "@/lib/apis/roles";

export const generatePermissionActionColumns = ({
  onView,
}: {
  onView: (data: Permission) => void;
}): ColumnDef<Permission>[] => [
  {
    accessorKey: "permissionSubject",
    header: "Permission Subject",
    cell: ({ row }) => {
      const data = row.original;
      return capitalize(data.permissionSubject.name);
    },
  },
  {
    accessorKey: "action",
    header: "Action",
  },
  {
    accessorKey: "description",
    header: "Description",
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const data = row.original;
      return <TableViewButton type="button" onClick={() => onView(data)} />;
    },
  },
];
