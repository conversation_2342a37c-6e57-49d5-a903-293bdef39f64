import { SignedIn, SignedOut } from "@clerk/nextjs";
import dynamic from "next/dynamic";
import type { PropsWithChildren } from "react";

import { AuthStateListener } from "@/components/auth-state-listener";
import { SDKInitializer } from "@/components/sdk-initializer";
import { useInactivity as UseInactive } from "@/hooks/use-inactive";

import { LayoutContent } from "./layout-content";
import { DashboardNavbar } from "./navbar";
import { DashboardSidebar } from "./sidebar";

const SignInPageContent = dynamic(
  () =>
    import("@/components/features/sign-in").then(
      (mod) => mod.SignInPageContent,
    ),
  { ssr: false },
);

const Authenticate = dynamic(
  () => import("./_authenticate").then((mod) => mod.Authenticate),
  { ssr: false },
);

export default async function DashboardLayout({ children }: PropsWithChildren) {
  return (
    <>
      <SignedIn>
        <Authenticate>
          <DashboardNavbar />
          <SDKInitializer />
          <AuthStateListener />
          <div className="mt-16 flex items-start">
            <DashboardSidebar />
            <LayoutContent>{children}</LayoutContent>
          </div>
          <UseInactive />
        </Authenticate>
      </SignedIn>
      <SignedOut>
        <SignInPageContent />
      </SignedOut>
    </>
  );
}
