"use client";

import { Card } from "flowbite-react";
import {
  <PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  TooltipContentProps,
} from "recharts";
import {
  NameType,
  ValueType,
} from "recharts/types/component/DefaultTooltipContent";
import { useMediaQuery } from "usehooks-ts";

import { Skeleton } from "@/components/ui/skeleton";
import { createRandomArray } from "@/lib/utils";

import { snakeCaseToCapitalize } from "../../../../components";
import { useUploadsByOriginationType } from "../hooks/use-document-analytics-queries";

const generateContrastingColor = (index: number) => {
  const baseColors = [
    { light: "#1e40af", dark: "#60a5fa" }, // blue-800 / blue-400
    { light: "#059669", dark: "#34d399" }, // emerald-600 / emerald-400
    { light: "#d97706", dark: "#fbbf24" }, // amber-600 / amber-400
    { light: "#dc2626", dark: "#f87171" }, // red-600 / red-400
    { light: "#7c3aed", dark: "#a78bfa" }, // violet-600 / violet-400
    { light: "#0891b2", dark: "#22d3ee" }, // cyan-600 / cyan-400
    { light: "#ea580c", dark: "#fb923c" }, // orange-600 / orange-400
    { light: "#65a30d", dark: "#a3e635" }, // lime-600 / lime-400
  ];

  if (index < baseColors.length) {
    return baseColors[index];
  }

  const hue = ((index - baseColors.length) * 137.5) % 360;
  return {
    light: `hsl(${hue}, 70%, 35%)`,
    dark: `hsl(${hue}, 70%, 65%)`,
  };
};

const getChartColor = (index: number) => {
  const colorPair = generateContrastingColor(index);
  return {
    light: colorPair.light,
    dark: colorPair.dark,
    default: colorPair.light,
  };
};

const CustomTooltip = ({
  active,
  payload,
}: TooltipContentProps<ValueType, NameType>) => {
  if (active && payload.length) {
    return (
      <div className="rounded-lg border bg-white p-3 shadow-lg dark:border-gray-600 dark:bg-gray-800">
        {payload.map((entry, index: number) => (
          <p
            key={index}
            className="text-sm text-black dark:text-white"
            style={{ color: entry.color }}
          >
            {`${snakeCaseToCapitalize(entry.name)}: ${entry.value}`}
          </p>
        ))}
      </div>
    );
  }
  return null;
};

export const OriginationTypeChart = () => {
  const isMobile = useMediaQuery("(width <= 640px)");
  const {
    data: originationData,
    isPending,
    isSuccess,
  } = useUploadsByOriginationType();

  const totalUploads =
    originationData?.reduce((sum, item) => sum + item.count, 0) || 0;

  return (
    <div>
      <h3 className="mb-4 text-lg font-medium text-gray-900 dark:text-white">
        Uploads by Origination Type
      </h3>

      {isPending ? (
        <OriginationTypeChartSkeleton />
      ) : (
        <div className="grid grid-cols-1 gap-6 xl:grid-cols-3">
          <Card className="xl:col-span-2 [&>div]:p-4 sm:[&>div]:p-6">
            <div className="mb-4 text-center">
              <h4 className="text-lg font-medium text-gray-900 dark:text-white">
                Upload Distribution
              </h4>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Total: {totalUploads} uploads
              </p>
            </div>
            <div className="h-80">
              {isSuccess && originationData?.length === 0 ? (
                <div className="flex h-80 items-center justify-center">
                  <p className="text-xl text-gray-500 dark:text-gray-400">
                    No data found
                  </p>
                </div>
              ) : (
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart accessibilityLayer>
                    <Pie
                      data={originationData ?? []}
                      outerRadius={isMobile ? 100 : 140}
                      innerRadius={isMobile ? 70 : 110}
                      fill="#8884d8"
                      dataKey="count"
                      nameKey="type"
                      paddingAngle={2}
                    >
                      {originationData?.map((_, index) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={getChartColor(index).default}
                        />
                      ))}
                    </Pie>
                    <Tooltip content={CustomTooltip} />
                  </PieChart>
                </ResponsiveContainer>
              )}
            </div>
          </Card>
          <Card className="[&>div]:justify-start">
            <h4 className="mb-4 text-lg font-medium text-gray-900 dark:text-white">
              Origination Breakdown
            </h4>
            <div className="max-h-80 space-y-3 overflow-y-auto pr-2">
              {originationData?.map((item, index) => {
                return (
                  <div
                    key={item.type}
                    className="flex items-center justify-between"
                  >
                    <div className="flex min-w-0 items-center space-x-3">
                      <div
                        className="h-4 w-4 rounded-full"
                        style={{
                          backgroundColor: getChartColor(index).light,
                        }}
                      />
                      <span className="truncate text-sm font-medium text-gray-900 dark:text-white">
                        {snakeCaseToCapitalize(item.type)}
                      </span>
                    </div>
                    <span className="text-sm font-bold text-gray-900 dark:text-white">
                      {item.count}
                    </span>
                  </div>
                );
              })}
            </div>
          </Card>
        </div>
      )}
    </div>
  );
};

const OriginationTypeChartSkeleton = () => (
  <div className="grid grid-cols-1 gap-6 xl:grid-cols-3">
    <Card className="xl:col-span-2">
      <div className="mb-4 text-center">
        <Skeleton className="mx-auto mb-2 h-6 w-32" />
        <Skeleton className="mx-auto h-4 w-40" />
      </div>
      <div className="flex h-80 items-center justify-center">
        <Skeleton className="size-48 rounded-full" />
      </div>
    </Card>
    <Card>
      <Skeleton className="mb-4 h-6 w-40" />
      <div className="max-h-80 space-y-3">
        {createRandomArray(6).map((_, index) => (
          <div key={index} className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Skeleton className="h-4 w-4 rounded-full" />
              <Skeleton className="h-5 w-32" />
            </div>
            <Skeleton className="h-5 w-8" />
          </div>
        ))}
      </div>
    </Card>
  </div>
);
