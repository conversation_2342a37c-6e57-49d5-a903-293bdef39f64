import { parseAsString, useQueryState } from "nuqs";
import React from "react";
import { FaRegFileAlt } from "react-icons/fa";
import { MdOutlineBrightnessAuto } from "react-icons/md";

import { Button } from "@/components/ui/button";
import { TabsWrapper } from "@/components/ui/tabs-wrapper";

import { AutoCategorizationTab } from "./tabs/auto-categorization";
import { DocumentAnalyticsTab } from "./tabs/document-analytics";

const ANALYTICS_TABS = [
  {
    key: "documents",
    title: (
      <span className="flex items-center gap-4">
        <FaRegFileAlt className="h-5 w-5 " />
        Document Analytics
      </span>
    ),
    content: <DocumentAnalyticsTab />,
  },
  {
    key: "categorization",
    title: (
      <span className="flex items-center gap-4">
        <MdOutlineBrightnessAuto className="h-5 w-5" />
        Auto Categorization
      </span>
    ),
    content: <AutoCategorizationTab />,
  },
];

export const AnalyticsTab = () => {
  const [subQuery, setSubQuery] = useQueryState(
    "subTab",
    parseAsString.withDefault("documents"),
  );

  const activeIndex = ANALYTICS_TABS.findIndex((tab) => tab.key === subQuery);

  return (
    <div className="space-y-4">
      <div className="flex flex-col gap-4 sm:flex-row">
        {ANALYTICS_TABS.map((tab) => (
          <Button
            onClick={() => setSubQuery(tab.key)}
            key={tab.key}
            variant={subQuery === tab.key ? "primary" : "outline"}
          >
            {tab.title}
          </Button>
        ))}
      </div>
      {ANALYTICS_TABS[activeIndex].content}
    </div>
  );
};
