import { useParams } from "next/navigation";
import { z } from "zod";

import { useStudies } from "@/components/features/studies/hooks/use-studies";
import { <PERSON><PERSON>, CloseButton } from "@/components/ui/button";
import { Form, Select } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { Modal } from "@/components/ui/modal";

import { useAssignmentAddStudy } from "../../../hooks/use-assignment-add-study";

const addStudySchema = z.object({
  studyId: z
    .string({ required_error: "Study is required" })
    .min(1, "Study is required"),
});

type ModalAddStudiesProps = {
  isOpen: boolean;
  onClose: () => void;
};

export const ModalAddStudies = ({ isOpen, onClose }: ModalAddStudiesProps) => {
  const params = useParams();
  const assignmentId = params.id as string;

  const { mutateAsync: addStudy, isPending: isAddingStudy } =
    useAssignmentAddStudy();

  async function onSubmit(data: z.infer<typeof addStudySchema>) {
    await addStudy({ assignmentId: assignmentId, studyId: data.studyId });
    onClose();
  }

  return (
    <Modal show={isOpen} onClose={onClose}>
      <Modal.Header>Add Study</Modal.Header>
      <Modal.Body>
        <Form
          mode="onChange"
          schema={addStudySchema}
          onSubmit={onSubmit}
          formProps={{ shouldFocusError: false }}
        >
          <StudyForm onClose={onClose} isSubmitting={isAddingStudy} />
        </Form>
      </Modal.Body>
    </Modal>
  );
};

type StudyFormProps = {
  onClose: () => void;
  isSubmitting?: boolean;
};

const StudyForm = ({ onClose, isSubmitting }: StudyFormProps) => {
  const { data: studiesData } = useStudies();

  return (
    <div className="grid gap-6">
      <div className="flex flex-col gap-2">
        <Label htmlFor="studyId">Study</Label>
        <Select
          id="studyId"
          name="studyId"
          placeholder="Select Study"
          options={studiesData?.results?.map((study) => ({
            label: study.name,
            value: study.id,
          }))}
        />
      </div>

      <div className="mt-6 flex flex-col gap-5 border-none pt-0 sm:flex-row">
        <Button type="submit" color="blue" isLoading={isSubmitting}>
          Save
        </Button>
        <CloseButton onClose={onClose} />
      </div>
    </div>
  );
};
