import { endOfDay, startOfDay } from "date-fns";
import { parseAsString, useQueryState } from "nuqs";

import { usePagination } from "@/hooks/use-pagination";

export const useHighLevelSummaryFilters = () => {
  const [sponsorId, setSponsorId] = useQueryState("sponsorId", parseAsString);
  const [studyId, setStudyId] = useQueryState("studyId", parseAsString);
  const [fromDate, setFromDate] = useQueryState("fromDate", parseAsString);
  const [toDate, setToDate] = useQueryState("toDate", parseAsString);

  const filters = {
    sponsorId: sponsorId || undefined,
    studyId: studyId || undefined,
    fromDate: fromDate
      ? startOfDay(new Date(fromDate)).toISOString()
      : undefined,
    toDate: toDate ? endOfDay(new Date(toDate)).toISOString() : undefined,
  };

  return {
    sponsorId,
    studyId,
    fromDate,
    toDate,
    setSponsorId,
    setStudyId,
    setFromDate,
    setToDate,
    filters,
  };
};

export const useDocumentAnalyticsFilters = () => {
  const [sourceType, setSourceType] = useQueryState(
    "sourceType",
    parseAsString.withDefault("source_documents"),
  );
  const [sponsorId, setSponsorId] = useQueryState("sponsorId", parseAsString);
  const [studyId, setStudyId] = useQueryState("studyId", parseAsString);
  const [fromDate, setFromDate] = useQueryState("fromDate", parseAsString);
  const [toDate, setToDate] = useQueryState("toDate", parseAsString);

  const { page, take, goToPage } = usePagination();

  const filters = {
    sponsorId: sponsorId || undefined,
    studyId: studyId || undefined,
    fromDate: fromDate
      ? startOfDay(new Date(fromDate)).toISOString()
      : undefined,
    toDate: toDate ? endOfDay(new Date(toDate)).toISOString() : undefined,
    page,
    limit: take,
    filter: {
      sourceType,
    },
  };

  return {
    sourceType,
    sponsorId,
    studyId,
    fromDate,
    toDate,
    page,
    take,
    setSourceType,
    setSponsorId,
    setStudyId,
    setFromDate,
    setToDate,
    goToPage,
    filters,
  };
};
