"use client";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import { CiEdit } from "react-icons/ci";

import { BackButton } from "@/components/shared";
import { OverviewCard, OverviewItem } from "@/components/shared/overview-card";
import { ActiveStatusBadge } from "@/components/ui/badges";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { useBreadcrumbs } from "@/hooks/use-breadcrumb";
import { IsfTemplate } from "@/lib/apis/isf-templates";
import { formatDate } from "@/lib/utils";

import { useISFTemplate } from "../hooks/use-isf-template-queries";
import { TemplateModal } from "../template-modal";
import { ISFTemplateDetail } from "./isf-template-detail";

export const ISFTemplateContent = () => {
  const { id } = useParams();
  const { setCustomBreadcrumbs } = useBreadcrumbs();
  const [selectedTemplate, setSelectedTemplate] = useState<IsfTemplate | null>(
    null,
  );
  const { data, isPending } = useISFTemplate(id as string);

  useEffect(() => {
    setCustomBreadcrumbs([
      {
        label: "Isf Templates",
        href: "/isf-templates",
      },
      {
        label: data?.name || "Isf Template Detail",
        loading: isPending,
      },
    ]);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data, isPending]);

  return (
    <>
      <div className="flex flex-col gap-4 ">
        <div className="flex min-w-0 flex-1 items-center gap-3 sm:gap-4">
          <BackButton type="link" href="/isf-templates" />

          {isPending ? (
            <Skeleton className="h-7 w-40" />
          ) : (
            <h1 className="leading-7.5 flex-1 truncate text-lg font-semibold sm:text-xl dark:text-white">
              {data?.name || "ISF Template Detail"}
            </h1>
          )}
        </div>
        <OverviewCard
          title="Overview"
          rightContent={
            <Button
              onClick={() => {
                setSelectedTemplate(data ? data : null);
              }}
              disabled={isPending}
              variant="primary"
              className="px-2 sm:px-4"
            >
              <CiEdit />
              Edit Template
            </Button>
          }
        >
          {isPending ? (
            <OverViewSkeleton />
          ) : (
            <div className="grid grid-cols-2 gap-x-2 gap-y-4 sm:grid-cols-3">
              <OverviewItem label="Name" value={data?.name} />
              <OverviewItem label="Status">
                <ActiveStatusBadge isActive={data?.isActive ?? false} />
              </OverviewItem>
              <OverviewItem
                label="Created At"
                value={
                  data?.createdDate
                    ? formatDate(data?.createdDate, "LLL dd, yyyy")
                    : "N/A"
                }
              />
            </div>
          )}
        </OverviewCard>
      </div>
      <ISFTemplateDetail />

      {selectedTemplate && (
        <TemplateModal
          isOpen={!!selectedTemplate}
          onClose={() => setSelectedTemplate(null)}
          template={selectedTemplate}
        />
      )}
    </>
  );
};

const OverViewSkeleton = () => {
  return (
    <div className="grid grid-cols-2 gap-x-2 gap-y-4 sm:grid-cols-3">
      <div className=" space-y-1">
        <Skeleton className="h-6 w-14" />
        <Skeleton className="h-5 w-28" />
      </div>

      <div className=" space-y-1">
        <Skeleton className="h-6 w-14" />
        <Skeleton className="h-5 w-14" />
      </div>
      <div className=" space-y-1">
        <Skeleton className="h-6 w-20" />
        <Skeleton className="h-5 w-24" />
      </div>
    </div>
  );
};
