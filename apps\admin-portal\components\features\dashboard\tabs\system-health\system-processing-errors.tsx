"use client";

import { Card } from "flowbite-react";
import Link from "next/link";
import { BiError } from "react-icons/bi";
import {
  FiAlertTriangle,
  FiDatabase,
  FiFileText,
  FiMail,
} from "react-icons/fi";

import { Skeleton } from "@/components/ui/skeleton";

import { useSystemProcessingErrors } from "./hooks/use-system-health-queries";

const ERROR_CATEGORIES = [
  {
    key: "protocolContentErrors",
    href: "/dashboards/system-health?tab=processing-errors&sources=protocol_content",
    label: "Protocol Content Errors",
    icon: (
      <div className="rounded-lg bg-red-100 p-2 dark:bg-red-900/20">
        <FiFileText className="h-4 w-4 text-red-600 dark:text-red-400" />
      </div>
    ),
  },
  {
    key: "sourceDocumentContentErrors",
    href: "/dashboards/system-health?tab=processing-errors&sources=source_document_content",
    label: "Source Document Content Errors",
    icon: (
      <div className="rounded-lg bg-orange-100 p-2 dark:bg-orange-900/20">
        <FiDatabase className="h-4 w-4 text-orange-600 dark:text-orange-400" />
      </div>
    ),
  },
  {
    key: "emailProcessingErrors",
    href: "/dashboards/system-health?tab=processing-errors&sources=email_to_source_documents,email_to_isf,email_to_doc_exchange",
    label: "Email Processing Errors",
    icon: (
      <div className="rounded-lg bg-yellow-100 p-2 dark:bg-yellow-900/20">
        <FiMail className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
      </div>
    ),
  },
  {
    key: "emailToSourceErrors",
    href: "/dashboards/system-health?tab=processing-errors&sources=email_to_source_documents",
    label: "Email to Source Errors",
    icon: (
      <div className="rounded-lg bg-purple-100 p-2 dark:bg-purple-900/20">
        <FiAlertTriangle className="h-4 w-4 text-purple-600 dark:text-purple-400" />
      </div>
    ),
  },
  {
    key: "emailToISFErrors",
    href: "/dashboards/system-health?tab=processing-errors&sources=email_to_isf",
    label: "Email to ISF Errors",
    icon: (
      <div className="rounded-lg bg-blue-100 p-2 dark:bg-blue-900/20">
        <FiAlertTriangle className="h-4 w-4 text-blue-600 dark:text-blue-400" />
      </div>
    ),
  },
  {
    key: "emailToDocExchangeErrors",
    href: "/dashboards/system-health?tab=processing-errors&sources=email_to_doc_exchange",
    label: "Email to Doc Exchange Errors",
    icon: (
      <div className="rounded-lg bg-teal-100 p-2 dark:bg-teal-900/20">
        <FiAlertTriangle className="h-4 w-4 text-teal-600 dark:text-teal-400" />
      </div>
    ),
  },
] as const;

export const SystemProcessingErrors = () => {
  const { data, isPending } = useSystemProcessingErrors();

  if (isPending) {
    return <SystemProcessingErrorsSkeleton />;
  }

  return (
    <>
      <Card className="h-full">
        <div className="mb-6 flex items-center justify-between">
          <Link
            href="/dashboards/system-health?tab=processing-errors"
            className="hover:text-primary-500 dark:hover:text-primary-500 text-lg font-semibold text-gray-900 hover:underline dark:text-white"
          >
            System Processing Errors
          </Link>
          <BiError className="h-6 w-6 text-red-500" />
        </div>

        <div className="mb-6 flex items-center justify-between rounded-lg border border-red-200 bg-red-50 p-4 dark:border-red-800 dark:bg-red-900/10">
          <p className="text-sm font-medium text-red-800 dark:text-red-200">
            Total Processing Errors
          </p>
          <span className="text-3xl font-bold text-red-600 dark:text-red-400">
            {data?.total ?? 0}
          </span>
        </div>

        <div className="space-y-3">
          <h4 className="mb-3 text-sm font-medium text-gray-700 dark:text-gray-300">
            Error Breakdown by Category
          </h4>

          <div className="grid grid-cols-1 gap-3 md:grid-cols-2">
            {ERROR_CATEGORIES.map((category) => {
              const count = data?.[category.key] ?? 0;
              return (
                <Link
                  href={category.href}
                  key={category.key}
                  className="flex cursor-pointer items-center justify-between gap-2 rounded-lg border border-gray-200 bg-gray-50 p-3 transition-colors hover:bg-gray-100 dark:border-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700"
                >
                  <p className="flex min-w-0 flex-1 items-center gap-3">
                    {category.icon}
                    <span className="min-w-0 flex-1 truncate text-sm font-medium text-gray-900 dark:text-white">
                      {category.label}
                    </span>
                  </p>
                  <span className="flex-shrink-0 text-lg font-semibold text-red-600 dark:text-red-400">
                    {count}
                  </span>
                </Link>
              );
            })}
          </div>
        </div>
      </Card>
    </>
  );
};

export const SystemProcessingErrorsSkeleton = () => {
  return (
    <Card className="h-full">
      <div className="mb-6 flex items-center justify-between">
        <Skeleton className="h-6 w-48" />
        <Skeleton className="h-6 w-6 rounded-full" />
      </div>

      <div className="mb-6 rounded-lg border border-red-200 bg-red-50 p-4 dark:border-red-800 dark:bg-red-900/10">
        <div className="flex items-center justify-between">
          <Skeleton className="h-4 w-32" />
          <Skeleton className="h-8 w-12" />
        </div>
      </div>

      <div className="space-y-3">
        <Skeleton className="h-4 w-40" />

        <div className="grid grid-cols-1 gap-3 md:grid-cols-2">
          {Array.from({ length: 6 }).map((_, index) => (
            <div
              key={index}
              className="flex items-center justify-between gap-2 rounded-lg border border-gray-200 bg-gray-50 p-3 dark:border-gray-700 dark:bg-gray-800"
            >
              <div className="flex min-w-0 flex-1 items-center gap-3">
                <Skeleton className="h-8 w-8 rounded-lg" />
                <Skeleton className="h-4 w-full" />
              </div>
              <Skeleton className="h-6 w-8" />
            </div>
          ))}
        </div>
      </div>
    </Card>
  );
};
