import type { ColumnDef } from "@tanstack/react-table";
import Link from "next/link";
import { IoMdEye } from "react-icons/io";

import type { Assignment } from "@/lib/apis/assignments";

export const columns: ColumnDef<Assignment>[] = [
  {
    accessorKey: "name",
    header: "Name",
  },
  {
    accessorKey: "type",
    header: "Type",
  },
  {
    accessorKey: "group.name",
    header: "Group",
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      return (
        <div className="flex gap-4 text-xs text-blue-500">
          <Link
            href={`/assignments/${row.original.id}`}
            className="text-destructive flex items-center gap-1"
          >
            <span>View</span>
            <IoMdEye />
          </Link>
        </div>
      );
    },
  },
];
