"use client";

import { Card } from "flowbite-react";
import { usePathname, useSearchParams } from "next/navigation";
import { useRouter } from "next/navigation";
import { parseAsBoolean, parseAsString, useQueryState } from "nuqs";
import { useMemo, useState } from "react";
import { Hi<PERSON><PERSON><PERSON>, HiPlus, HiX } from "react-icons/hi";

import { HeaderActions } from "@/components/shared/header-actions";
import LoadingWrapper from "@/components/shared/loading-wrapper";
import { Button } from "@/components/ui/button";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table } from "@/components/ui/table";
import { useDisclosure } from "@/hooks/use-disclosure";
import { capitalize } from "@/lib/utils";

import { useArchiveRole } from "../hooks/use-roles-muataions";
import { useRoles } from "../hooks/use-roles-queries";
import { generateRoleColumns } from "./columns";
import { ModalFilter } from "./filter-modal";
import { RoleModal } from "./role-modal";

export type Role = {
  id: number;
  name: string;
  description?: string;
};

const RolePageContent = function () {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const { data, isPending, isPlaceholderData } = useRoles();
  const { mutate, isPending: isArchiving } = useArchiveRole();
  const [type] = useQueryState("type", parseAsString);
  const [isActive] = useQueryState("active", parseAsBoolean);
  const [name] = useQueryState("name", parseAsString);

  const [showModalAddUser, setShowModalAddUser] = useState(false);
  const {
    isOpen: isOpenFilterModal,
    close: onCloseFilterModal,
    open: onOpenFilterModal,
  } = useDisclosure();

  const appliedFilter = [
    { label: "Name", value: name, key: "name" },
    { label: "Active", value: isActive, key: "active" },
    { label: "Type", value: type ? capitalize(type) : type, key: "type" },
  ];

  const handleArchiveRole = (id: string) => {
    mutate(id);
  };

  const handleRemoveAFilter = (key: string) => {
    const currentParams = new URLSearchParams(
      Array.from(searchParams.entries()),
    );
    currentParams.delete(key);
    currentParams.set("page", "1");

    const query = currentParams ? `?${currentParams.toString()}` : "";
    router.push(`${pathname}${query}`);
  };

  const exportCSVData = useMemo(() => {
    if (!data?.results.length) return [];

    const headers = [
      { label: "ID", key: "id" },
      { label: "Name", key: "name" },
      { label: "Type", key: "type" },
      { label: "Active", key: "isActive" },

      { label: "Description", key: "description" },
    ];

    return [
      headers.map((header) => header.label),
      ...data.results.map((role) =>
        headers.map((header) => {
          return role[header.key as keyof Role];
        }),
      ),
    ];
  }, [data]);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const columns = useMemo(() => generateRoleColumns(handleArchiveRole), []);

  const renderTable = () => {
    if (isPending) {
      return (
        <Table
          columns={columns}
          data={Array(10)
            .fill(null)
            .map((_, index) => ({ id: `${index + 1}` }) as any)}
          headCellStyle="text-gray-500 p-4"
          isLoading={isPending}
        />
      );
    }
    return (
      <>
        <LoadingWrapper isLoading={isPlaceholderData || isArchiving}>
          <Table data={data?.results ?? []} columns={columns} />
        </LoadingWrapper>
        {data?.metadata && <TableDataPagination metadata={data.metadata} />}
      </>
    );
  };

  return (
    <>
      <div className="">
        <h1 className="mb-4 text-2xl font-semibold text-gray-900 sm:text-2xl dark:text-white">
          Roles
        </h1>
        <Card className="[&>div]:p-0">
          <HeaderActions
            data={exportCSVData}
            filename="users.csv"
            isShowedSearchInput={false}
          />

          <div className="mb-3 flex items-center justify-between px-3 sm:px-4">
            <Button
              variant="outline"
              className=" w-fit"
              onClick={onOpenFilterModal}
            >
              <HiFilter />
              <span className="relative">Filter</span>
            </Button>
            <Button
              variant="primary"
              className="flex-shrink-0 px-0 py-1.5"
              onClick={() => setShowModalAddUser(true)}
            >
              <HiPlus />
              Add Role
            </Button>
          </div>
          <div className="mb-3 flex max-w-full flex-wrap items-center gap-2 overflow-hidden px-3 sm:px-4">
            {appliedFilter.map((filter) => {
              if (!filter.value) return null;
              return (
                <div
                  key={filter.key}
                  className="flex items-center gap-1 rounded-full bg-gray-100 px-3 py-1 text-xs text-gray-700 dark:bg-gray-700 dark:text-gray-300"
                >
                  <span className="max-w-[160px] truncate">
                    {filter.label} :{" "}
                    {typeof filter.value === "boolean" ? "Yes" : filter.value}
                  </span>
                  <button
                    onClick={() => handleRemoveAFilter(filter.key)}
                    className="ml-1 flex-shrink-0 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600"
                  >
                    <HiX className="h-4 w-4" />
                  </button>
                </div>
              );
            })}
          </div>
          {renderTable()}
        </Card>
      </div>
      <RoleModal
        isOpen={showModalAddUser}
        onClose={() => setShowModalAddUser(false)}
      />
      <ModalFilter isOpen={isOpenFilterModal} onClose={onCloseFilterModal} />
    </>
  );
};

export default RolePageContent;
