"use client";
import type { ColumnDef } from "@tanstack/react-table";
import Link from "next/link";
import { MdOutlineRemoveRedEye } from "react-icons/md";
import { RiDeleteBin6Fill } from "react-icons/ri";

import { ActiveStatusBadge } from "@/components/ui/badges";
import { Role } from "@/lib/apis/roles";

export const generateRoleColumns = (
  onDelete: (id: string) => void,
): ColumnDef<Role>[] => [
  {
    header: "Name",
    accessorKey: "name",
    cell: ({ row }) => (
      <Link
        className="text-primary-500 cursor-pointer whitespace-nowrap underline-offset-4 hover:underline"
        href={`/roles/${row.original.id}`}
      >
        {row.original.name}
      </Link>
    ),
  },
  {
    header: "Description",
    accessorKey: "description",
  },
  {
    header: "Type",
    accessorKey: "type",
    cell: ({ row }) => {
      return <span className="uppercase">{row.original.type}</span>;
    },
  },
  {
    header: "Active",
    accessorKey: "isActive",
    cell: ({ getValue }) => {
      const value = getValue() as boolean;
      return <ActiveStatusBadge isActive={value} />;
    },
  },
  {
    header: "Action",
    accessorKey: "id",
    cell: ({ row }) => (
      <div className="flex items-center gap-x-3">
        <Link
          className="leading-4.5 text-primary-500 flex w-fit cursor-pointer items-center gap-1 text-xs font-medium"
          href={`/roles/${row.original.id}`}
        >
          <MdOutlineRemoveRedEye />
          <span className="whitespace-nowrap">View</span>
        </Link>
        <button
          onClick={() => onDelete(row.original.id)}
          className="flex items-center gap-1 rounded-md border-none text-red-500 outline-none"
        >
          <RiDeleteBin6Fill />
          Archive
        </button>
      </div>
    ),
  },
];
