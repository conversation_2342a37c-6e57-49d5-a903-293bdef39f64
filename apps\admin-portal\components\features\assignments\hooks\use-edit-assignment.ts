import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import type { UpdateAssignmentPayload } from "@/lib/apis/assignments";

import { USE_ASSIGNMENT_QUERY_KEY } from "./use-assignment";

export const useEditAssignment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: UpdateAssignmentPayload & { id: string }) => {
      const { id, ...rest } = payload;
      return api.assignments.update(id, rest);
    },
    onSuccess: (_, variables) => {
      toast.success("Assignment updated successfully");
      queryClient.invalidateQueries({
        queryKey: [USE_ASSIGNMENT_QUERY_KEY, variables.id],
      });
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
};
