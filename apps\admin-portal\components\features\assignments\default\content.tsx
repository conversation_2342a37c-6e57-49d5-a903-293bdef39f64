"use client";

import { Card } from "flowbite-react";
import { type FC, useMemo, useState } from "react";

import { HeaderActions } from "@/components/shared/header-actions";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table } from "@/components/ui/table";

import { useAssignments } from "../hooks/use-assignments";
import { columns } from "./columns";
import { ModalAddAssignment } from "./modal-add-assignment";

const AssignmentPageContent: FC = function () {
  const [showModalAddAssignment, setShowModalAddAssignment] = useState(false);
  const { data, isLoading } = useAssignments();

  const exportCSVData = useMemo(() => {
    if (!data || !data.results) return [];

    const headers = [
      { label: "ID", key: "id" },
      { label: "Name", key: "name" },
      { label: "Status", key: "status" },
    ];

    return [
      headers.map((header) => header.label),
      ...data.results.map((assignment) =>
        headers.map(
          (header) => assignment[header.key as keyof typeof assignment],
        ),
      ),
    ];
  }, [data]);

  return (
    <>
      <div className="">
        <h1 className="mb-4 text-2xl font-semibold text-gray-900 sm:text-2xl dark:text-white">
          Assignments
        </h1>
        <Card className="[&>div]:p-0">
          <HeaderActions
            data={exportCSVData}
            buttonText="Add Assignment"
            filename="assignments.csv"
            onButtonClick={() => setShowModalAddAssignment(true)}
          />
          {isLoading && (
            <Table
              columns={columns}
              data={Array(10)
                .fill(null)
                .map((_, index) => ({ id: `${index + 1}` }) as any)}
              headCellStyle="text-gray-500 p-4"
              isLoading={isLoading}
            />
          )}
          {data && (
            <>
              <Table data={data.results} columns={columns} />
              {data.metadata && (
                <TableDataPagination metadata={data.metadata} />
              )}
            </>
          )}
        </Card>
      </div>
      {/* TODO: Add ModalAddAssignment component */}
      {showModalAddAssignment && (
        <ModalAddAssignment
          isOpen={showModalAddAssignment}
          onClose={() => setShowModalAddAssignment(false)}
        />
      )}
    </>
  );
};

export default AssignmentPageContent;
