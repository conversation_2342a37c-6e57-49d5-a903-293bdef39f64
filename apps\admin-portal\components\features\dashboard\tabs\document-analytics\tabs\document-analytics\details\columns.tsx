import { ColumnDef } from "@tanstack/react-table";
import Link from "next/link";

import type { DocumentDetail } from "@/lib/apis/document-analytics";
import { formatDate } from "@/lib/utils";

const getDocumentTitle = (document: DocumentDetail) => {
  if (!document.siteId || !document.studyId) return document.title;
  if (document.sourceType === "doc_exchange_files")
    return (
      <Link
        className="text-primary-500 cursor-pointer underline-offset-4 hover:underline"
        href={`/sites/${document.siteId}?tab=doc-exchange`}
      >
        {document.title}
      </Link>
    );

  if (document.sourceType === "artifact_files")
    return (
      <Link
        className="text-primary-500 cursor-pointer underline-offset-4 hover:underline"
        href={`/sites/${document.siteId}/studies/${document.studyId}?tab=isf`}
      >
        {document.title}
      </Link>
    );
  if (document.sourceType === "source_documents")
    return document.patientId ? (
      <Link
        className="text-primary-500 cursor-pointer underline-offset-4 hover:underline"
        href={`/sites/${document.siteId}/studies/${document.studyId}/patients/${document.patientId}?tab=smr`}
      >
        {document.title}
      </Link>
    ) : (
      document.title
    );
};

export const documentColumns: ColumnDef<DocumentDetail>[] = [
  {
    accessorKey: "title",
    header: "Title",
    cell: ({ row }) => getDocumentTitle(row.original),
  },
  {
    accessorKey: "createdDate",
    header: "Created Date",
    cell: ({ row }) =>
      row.original.createdDate
        ? formatDate(row.original.createdDate, "MMM dd, yyyy")
        : "-",
  },
  {
    accessorKey: "studyName",
    header: "Study Name",
  },
  {
    accessorKey: "sponsorName",
    header: "Sponsor Name",
  },
  {
    accessorKey: "siteName",
    header: "Site Name",
  },
];
