---
description: Project-specific patterns and conventions
globs:
alwaysApply: false
---

# Project-Specific Patterns and Conventions

## Form Logic and Fields

- Filter field for study status should be a single-select rather than a multi-select component without an 'All' option
- When implementing patient forms, include an optional 'Study Arm' field using LazySelect component with a 'searchable' boolean prop defaulting to true
- For datepicker components, don't reset visit date when visit day or visit window changes, and show a warning message when visit date is outside the designated window
- When displaying valid date selections, show descriptive messages like '<PERSON> may schedule the visit between Monday, April 14 and Friday, April 25'
- Implement disabled state for datepicker components

## Component Architecture

- Create reusable components to avoid duplicating logic across similar forms
- Document preview components should be implemented in shared directories to be reusable and maintainable
- LazySelect component's focus state styling should match the pattern in apps/client-portal/components/ui/form/select.tsx for UI consistency
- Accordion components should use React context to manage open state across all subcomponents to improve customizability
- Implement collapse/expand functionality for 'Investigator Site File' with 'eBinder' as collapsible content, following the same pattern used for 'Study Patients'

## Document Management

- For document previews, implement keyboard navigation with left/right arrow keys with pagination support
- Disable the 'Create Placeholder' button when users have selected a file
- Patient consent management UI should include a table with columns for document metadata and actions
- Document exchange modal upload should use the useUppy hook instead of the custom useUppyInstance hook
- Implement strict upload for ebinder similar to doc exchange, allowing only specific file types with a 50MB size limit
- For eBinder, Doc Exchange, and Source Medical Records components, replace separate date pickers with a unified date range component

## Drag and Drop Implementation

- Use @dnd-kit library for implementing drag and drop functionality, particularly for folder tree components
- DndContext should be implemented at the module level rather than inside individual components
- For folder tree components, allow users to drag and drop folders among other folders to make them root folders
- After drag and drop operations, the dropped folder should become the currently selected folder and all parent folders should be opened
- When dragging folders or files, prevent infinite page height increase and be careful not to block scrolling with long folder lists
- When dragging a file, the row in the table should have 50% opacity
- Use DnD Sortable preset for implementing drag-and-drop reordering for activities in patient visit components
- When implementing drag and drop for accordion items, close all accordion items during the dragging operation

## UI/UX Enhancements

- Tooltips should include a triangle pointer underneath to enhance their visual appearance
- Add a 'Study Arm' column to the Patients page and Study Patients page, positioned to the right of the 'Start Date' column
- Add animations to dropdown lists using the tailwindcss-motion library, including gradual height increase effects
- When adding new folders, implement intelligent default naming that increments index for duplicates
- Apply folder tree overflow and height fixes consistently across components

## Email Handling

- Email address format should not have a space after module name and show toast errors when missing name or code
- Email content HTML/CSS styling should be scoped only to the EmailBody component to prevent affecting other parts of the project

## Code Style and Best Practices

- Use ts-pattern library for implementing conditional logic in the codebase
- Use useCallback hook for implementing callback functions in React components
- Prefer comprehensive solutions for handling UTF-8 text encoding rather than character-by-character replacement
- Implement custom hooks with keyboard shortcut support for functionality like undo operations
- For managing permissions for logged-in users on the Client Portal, create a global store using Zustand
- When implementing error handling with React Query, ensure errors are only displayed once when using token refresh retry logic without creating custom wrapper hooks

## API Integration

- When calling APIs to get lists of study-arms or epochs, always include 'isActive=true' filter parameter
- When implementing date filters, convert fromDueDate to start of day and toDueDate to end of day in filter parameters
