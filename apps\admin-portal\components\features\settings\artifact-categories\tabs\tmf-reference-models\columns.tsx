import { ColumnDef } from "@tanstack/react-table";

import {
  TableEditButton,
  TableRemoveButton,
} from "@/components/shared/table-action-buttons";
import { TMFRefModel } from "@/lib/apis/tmf-ref-models";

export const generateTmfColumns = ({
  onDelete,
  onEdit,
}: {
  onEdit: (data: TMFRefModel) => void;
  onDelete: (data: TMFRefModel) => void;
}): ColumnDef<TMFRefModel>[] => [
  {
    accessorKey: "id",
    header: "ID",
  },
  {
    accessorKey: "tmfRefModel",
    header: "Label",
  },
  {
    accessorKey: "description",
    header: "Description",
  },
  {
    header: "Actions",
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="flex gap-x-4">
          <TableEditButton type="button" onClick={() => onEdit(data)} />
          <TableRemoveButton onClick={() => onDelete(data)} />
        </div>
      );
    },
  },
];
