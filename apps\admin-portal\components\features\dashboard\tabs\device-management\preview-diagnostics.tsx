import React from "react";
import ReactJ<PERSON> from "react-json-view";

import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { Skeleton } from "@/components/ui/skeleton";

import { useScannerDiagnostics } from "./hooks/use-device-management-queries";

type Props = {
  id: string;
  isOpen: boolean;
  onClose: () => void;
};

export const PreviewDiagnosticsModal = ({ id, isOpen, onClose }: Props) => {
  const { data, isPending } = useScannerDiagnostics(id);

  return (
    <WrapperModal
      isOpen={isOpen}
      onClose={onClose}
      title="Scanner Diagnostics"
      size="4xl"
    >
      {isPending ? (
        <Skeleton className="h-[40vh] w-full" />
      ) : !data?.diagnostics ? (
        <>
          <div className="grid h-[40vh] place-content-center text-2xl font-bold text-gray-500 dark:text-gray-400">
            No Diagnostics Found
          </div>
        </>
      ) : (
        <ReactJson
          style={{
            minHeight: "40vh",
          }}
          theme="harmonic"
          src={data?.diagnostics}
          displayObjectSize={false}
          displayDataTypes={false}
        />
      )}
    </WrapperModal>
  );
};
