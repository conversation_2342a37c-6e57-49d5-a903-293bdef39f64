import { endOfDay, startOfDay } from "date-fns";
import { parseAsString, useQueryState } from "nuqs";

import { usePagination } from "@/hooks/use-pagination";

export const useAutoCategorizationFilters = () => {
  const [sponsorId, setSponsorId] = useQueryState("sponsorId", parseAsString);
  const [studyId, setStudyId] = useQueryState("studyId", parseAsString);
  const [fromDate, setFromDate] = useQueryState("fromDate", parseAsString);
  const [toDate, setToDate] = useQueryState("toDate", parseAsString);
  const [methodType, setMethodType] = useQueryState("methodType", parseAsString);
  const [status, setStatus] = useQueryState("status", parseAsString);

  const { page, take, goToPage } = usePagination();

  const filters = {
    sponsorId: sponsorId || undefined,
    studyId: studyId || undefined,
    fromDate: fromDate
      ? startOfDay(new Date(fromDate)).toISOString()
      : undefined,
    toDate: toDate ? endOfDay(new Date(toDate)).toISOString() : undefined,
    methodType: methodType || undefined,
    status: status || undefined,
  };

  return {
    sponsorId,
    studyId,
    fromDate,
    toDate,
    methodType,
    status,
    page,
    take,
    setSponsorId,
    setStudyId,
    setFromDate,
    setToDate,
    setMethodType,
    setStatus,
    goToPage,
    filters,
  };
};
