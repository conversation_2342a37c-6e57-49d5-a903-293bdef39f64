"use client";

import { Params } from "next/dist/shared/lib/router/utils/route-matcher";
import { useParams } from "next/navigation";
import { CiEdit } from "react-icons/ci";

import { OverviewCard, OverviewItem } from "@/components/shared/overview-card";
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { useDisclosure } from "@/hooks/use-disclosure";
import { useProtocol } from "@/hooks/use-protocol";
import { ProtocolEncounter } from "@/lib/apis/protocols";
import { useEncounterStore } from "@/stores/encounte-store";

import { ModalEditEncounter } from "../modal-edit-encounter";
import { useProtocolEncounter } from "./hooks/use-protocol-encounter";
import { EncounterTabs } from "./tabs";

interface EncounterDetailParams extends Params {
  id: string;
  protocolId: string;
}

export const EncounterDetailContent = () => {
  const params = useParams<EncounterDetailParams>();
  const { currentEncounter } = useEncounterStore();
  const { protocolId } = params;
  const { isOpen, open, close } = useDisclosure();
  const { data: protocol } = useProtocol(protocolId);

  const { data: encounter, isLoading: isLoadingEncounter } =
    useProtocolEncounter(protocolId, currentEncounter?.id);

  return (
    <>
      <div className="flex flex-col gap-4 ">
        <div className="flex items-center justify-between">
          <div className="text-lg font-bold dark:text-gray-300">
            {encounter?.name ?? "N/A"}
          </div>
          <Button
            disabled={protocol?.isPublished}
            variant="primary"
            onClick={open}
          >
            <CiEdit />
            Edit Visit
          </Button>
        </div>
        {isLoadingEncounter ? (
          <EncounterContentSkeleton />
        ) : (
          <OverviewCard title="Overview">
            <div className="grid grid-cols-2 gap-4">
              <OverviewItem label="Name" value={encounter?.name ?? "N/A"} />
              <OverviewItem
                label="Published Date"
                value={encounter?.publishedDate ?? "N/A"}
              />
              <OverviewItem
                label="Visit Type"
                value={encounter?.visitType.name ?? "N/A"}
              />
            </div>
          </OverviewCard>
        )}
        <EncounterTabs totalActivities={encounter?.totalActivities} />
      </div>
      <ModalEditEncounter
        encounter={encounter as ProtocolEncounter}
        isOpen={isOpen}
        onClose={close}
      />
    </>
  );
};

const EncounterContentSkeleton = () => {
  return (
    <div className="flex flex-col">
      <div className="rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-700 dark:bg-gray-800">
        <Skeleton className="mb-3 h-6 w-24" />
        <div className="grid grid-cols-2 gap-4">
          <div className="flex flex-col gap-1">
            <Skeleton className="h-5 w-16" />
            <Skeleton className="h-4 w-32" />
          </div>
          <div className="flex flex-col gap-1">
            <Skeleton className="h-5 w-16" />
            <Skeleton className="h-4 w-20" />
          </div>
          <div className="flex flex-col gap-1">
            <Skeleton className="h-5 w-24" />
            <Skeleton className="h-4 w-32" />
          </div>
          <div className="flex flex-col gap-1">
            <Skeleton className="h-5 w-20" />
            <Skeleton className="h-4 w-32" />
          </div>
          <div className="flex flex-col gap-1">
            <Skeleton className="h-5 w-40" />
            <Skeleton className="h-4 w-32" />
          </div>
        </div>
      </div>
    </div>
  );
};
