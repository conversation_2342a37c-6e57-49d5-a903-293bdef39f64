import { useMutation } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

import api from "@/lib/apis";

import { isfTemplateKeys } from "./use-isf-template-queries";

export const useCreateTemplate = () =>
  useMutation({
    mutationFn: (payload: { name: string }) =>
      api.isfTemplates.createTemplate(payload),
    onError: (error) => {
      toast.error(error.message ?? "Failed to create template");
    },
    onSettled: (_, err) =>
      !err && toast.success("Template created successfully"),
    meta: {
      awaits: isfTemplateKeys.allList(),
    },
  });

export const useUpdateTemplate = (id?: string) =>
  useMutation({
    mutationFn: (payload: { id: string; name: string; isActive: boolean }) =>
      api.isfTemplates.updateTemplate(payload),
    onError: (error) => {
      toast.error(error.message ?? "Failed to update template");
    },
    onSettled: (_, err) =>
      !err && toast.success("Template updated successfully"),
    meta: {
      awaits: [isfTemplateKeys.allList(), id ? isfTemplateKeys.detail(id) : []],
    },
  });

export const useDeleteTemplate = () =>
  useMutation({
    mutationFn: (id: string) => api.isfTemplates.deleteTemplate(id),
    onError: (error) => {
      toast.error(error.message ?? "Failed to delete template");
    },
    onSettled: (_, err) =>
      !err && toast.success("Template deleted successfully"),
    meta: {
      awaits: isfTemplateKeys.allList(),
    },
  });
