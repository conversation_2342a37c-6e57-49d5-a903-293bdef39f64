import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import type { AddAssignmentPayload } from "@/lib/apis/assignments";

import { USE_ASSIGNMENTS_QUERY_KEY } from "./use-assignments";

export const useAddAssignment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: AddAssignmentPayload) =>
      api.assignments.create(payload),
    onSuccess: () => {
      toast.success("Assignment created successfully");
      queryClient.invalidateQueries({ queryKey: [USE_ASSIGNMENTS_QUERY_KEY] });
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
};
