import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";

import { USE_ASSIGNMENT_STUDIES_QUERY_KEY } from "./use-assignment-studies";

export const useAssignmentRemoveStudy = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: { assignmentId: string; studyId: string }) =>
      api.assignments.removeStudy(payload.assignmentId, payload.studyId),
    onSuccess: (_, variables) => {
      toast.success("Study removed from assignment");
      queryClient.invalidateQueries({
        queryKey: [USE_ASSIGNMENT_STUDIES_QUERY_KEY, variables.assignmentId],
      });
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
};
