"use client";

import { parseAsString, useQueryState } from "nuqs";
import {
  FiClipboard,
  FiDatabase,
  FiFileText,
  FiMapPin,
  FiUsers,
} from "react-icons/fi";

import { PatientsInactiveStudySiteTab } from "@/components/features/dashboard/tabs/configurations/detail/tabs/patients-inactive-study-site";
import { ProtocolsNoStudyTab } from "@/components/features/dashboard/tabs/configurations/detail/tabs/protocols-no-study";
import { SitesNoAssignedUserTab } from "@/components/features/dashboard/tabs/configurations/detail/tabs/sites-no-assigned-user";
import { StudiesNoProtocolTab } from "@/components/features/dashboard/tabs/configurations/detail/tabs/studies-no-protocol";
import { TasksInactiveUsersGroupsTab } from "@/components/features/dashboard/tabs/configurations/detail/tabs/tasks-inactive-users-groups";
import { BackButton } from "@/components/shared";
import { UncontrolledSelect } from "@/components/ui/form/select/uncontrolled-select";
import { TabsWrapper } from "@/components/ui/tabs-wrapper";

const CONFIGURATION_TABS = [
  {
    key: "studies-no-protocol",
    title: (
      <p className="flex items-center gap-2">
        <FiDatabase className="h-4 w-4 text-blue-500" />
        Studies No Protocol
      </p>
    ),
    content: <StudiesNoProtocolTab />,
  },
  {
    key: "sites-no-assigned-user",
    title: (
      <p className="flex items-center gap-2">
        <FiMapPin className="h-4 w-4 text-green-500" />
        Sites No Assigned User
      </p>
    ),
    content: <SitesNoAssignedUserTab />,
  },
  {
    key: "protocols-no-study",
    title: (
      <p className="flex items-center gap-2">
        <FiFileText className="h-4 w-4 text-purple-500" />
        Protocols No Study
      </p>
    ),
    content: <ProtocolsNoStudyTab />,
  },
  {
    key: "patients-inactive-study-site",
    title: (
      <p className="flex items-center gap-2">
        <FiUsers className="h-4 w-4 text-orange-500" />
        Patients Inactive Study/Site
      </p>
    ),
    content: <PatientsInactiveStudySiteTab />,
  },
  {
    key: "tasks-inactive-users-groups",
    title: (
      <p className="flex items-center gap-2">
        <FiClipboard className="h-4 w-4 text-red-500" />
        Tasks Inactive Users/Groups
      </p>
    ),
    content: <TasksInactiveUsersGroupsTab />,
  },
] as const;

const SELECT_OPTIONS = CONFIGURATION_TABS.map((tab) => ({
  label: tab.title,
  value: tab.key,
}));

export default function ConfigurationsDashboardPage() {
  const [currentTab, setCurrentTab] = useQueryState("tab", parseAsString);

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <BackButton type="link" href="/dashboards?tab=configurations" />
        <h1 className="text-2xl font-semibold text-gray-900 sm:text-3xl dark:text-white">
          Configurations & Data Integrity
        </h1>
      </div>

      <div className="space-y-4 sm:hidden">
        <UncontrolledSelect
          placeholder="Select configuration section"
          options={SELECT_OPTIONS}
          value={currentTab || ""}
          onChange={(value) => {
            if (value) {
              setCurrentTab(value);
            }
          }}
          className="w-full"
        />

        <div>
          {CONFIGURATION_TABS.find((tab) => tab.key === currentTab)?.content ??
            CONFIGURATION_TABS[0].content}
        </div>
      </div>

      <div className="hidden sm:block">
        <TabsWrapper
          className="flex-nowrap overflow-x-auto [&>button]:flex-shrink-0"
          tabs={CONFIGURATION_TABS}
        />
      </div>
    </div>
  );
}
