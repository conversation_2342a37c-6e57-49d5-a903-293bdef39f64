"use client";

import { ColumnDef, SortingState } from "@tanstack/react-table";
import { Card } from "flowbite-react";
import { useState } from "react";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { PillBadge } from "@/components/ui/badges/pill-badge";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";
import { CategorizationMethod } from "@/lib/apis/auto-categorization";
import { formatDate } from "@/lib/utils";

import { useCategorizationMethods } from "../hooks/use-auto-categorization-queries";
import { CategorizationMethodTableFilter } from "./categorization-method-table-filter";

const getStatusBadgeColor = (status: string) => {
  switch (status) {
    case "active":
      return "green";
    case "inactive":
      return "gray";
    case "draft":
      return "yellow";
    default:
      return "gray";
  }
};

const getMethodTypeBadgeColor = (methodType: string) => {
  switch (methodType) {
    case "ai":
      return "blue";
    case "rule-based":
      return "purple";
    case "hybrid":
      return "indigo";
    default:
      return "gray";
  }
};

const columns: ColumnDef<CategorizationMethod>[] = [
  {
    accessorKey: "name",
    header: "Method Name",
    cell: ({ row }) => (
      <div className="flex flex-col">
        <span className="font-medium text-gray-900 dark:text-white">
          {row.original.name}
        </span>
        {row.original.description && (
          <span className="text-sm text-gray-500 dark:text-gray-400">
            {row.original.description}
          </span>
        )}
      </div>
    ),
  },
  {
    accessorKey: "methodType",
    header: "Type",
    cell: ({ row }) => (
      <PillBadge
        color={getMethodTypeBadgeColor(row.original.methodType)}
        text={row.original.methodType.toUpperCase()}
      />
    ),
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => (
      <PillBadge
        color={getStatusBadgeColor(row.original.status)}
        text={row.original.status.toUpperCase()}
      />
    ),
  },
  {
    accessorKey: "accuracy",
    header: "Accuracy",
    cell: ({ row }) => (
      <span className="font-medium">
        {(row.original.accuracy * 100).toFixed(1)}%
      </span>
    ),
  },
  {
    accessorKey: "totalCategorizations",
    header: "Total Uses",
    cell: ({ row }) => (
      <span className="text-gray-900 dark:text-white">
        {row.original.totalCategorizations.toLocaleString()}
      </span>
    ),
  },
  {
    accessorKey: "averageConfidence",
    header: "Avg Confidence",
    cell: ({ row }) => (
      <span className="font-medium">
        {(row.original.averageConfidence * 100).toFixed(1)}%
      </span>
    ),
  },
  {
    accessorKey: "version",
    header: "Version",
    cell: ({ row }) => (
      <span className="text-sm font-mono text-gray-600 dark:text-gray-300">
        v{row.original.version}
      </span>
    ),
  },
  {
    accessorKey: "lastUsedDate",
    header: "Last Used",
    cell: ({ row }) => (
      <span className="text-sm text-gray-600 dark:text-gray-300">
        {row.original.lastUsedDate
          ? formatDate(row.original.lastUsedDate, "MMM dd, yyyy")
          : "Never"}
      </span>
    ),
  },
];

export const CategorizationMethodTable = () => {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [currentPage, setCurrentPage] = useState(1);
  
  const { data, isPending, isPlaceholderData } = useCategorizationMethods(
    sorting,
    { page: currentPage }
  );

  return (
    <Card className="[&>div]:p-0">
      <div className="flex items-center justify-between border-b border-gray-200 p-4 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Categorization Methods
        </h3>
        <CategorizationMethodTableFilter />
      </div>

      <LoadingWrapper
        isLoading={isPending}
        fallback={<TableLoading />}
        className="min-h-[400px]"
      >
        <Table
          data={data?.data || []}
          columns={columns}
          sorting={sorting}
          onSortingChange={setSorting}
          isPlaceholderData={isPlaceholderData}
        />
      </LoadingWrapper>

      {data?.metadata && (
        <div className="border-t border-gray-200 p-4 dark:border-gray-700">
          <TableDataPagination
            currentPage={currentPage}
            totalPages={data.metadata.totalPages}
            totalItems={data.metadata.totalCount}
            onPageChange={setCurrentPage}
            isPlaceholderData={isPlaceholderData}
          />
        </div>
      )}
    </Card>
  );
};
