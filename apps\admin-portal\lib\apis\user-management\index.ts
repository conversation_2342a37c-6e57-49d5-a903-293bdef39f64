import BaseApi from "../base";
import { MetadataParams } from "../types";
import {
  LoginActivityResponse,
  ProfileWithoutRoleResponse,
  RoleDistributionResponse,
  UserNeverLoginResponse,
  UserStatisticResponse,
  UsersWithoutProfileResponse,
} from "./types";

class UserManagementApi extends BaseApi {
  constructor() {
    super("/user-management", true);
  }

  async getUserStatistic() {
    return this.http.get<UserStatisticResponse>(`/user-statistics`);
  }
  async getLoginActivity(params?: { fromDate?: string; toDate?: string }) {
    return this.http.get<LoginActivityResponse>(`/login-activity`, params);
  }

  async getRoleDistribution() {
    return this.http.get<RoleDistributionResponse>(`/role-distribution`);
  }

  async getUsersWithoutProfile(params?: MetadataParams) {
    return this.http.get<UsersWithoutProfileResponse>(
      `/users-without-profile`,
      params,
    );
  }
  async getProfilesWithoutRole(params?: MetadataParams) {
    return this.http.get<ProfileWithoutRoleResponse>(
      `/profiles-without-role`,
      params,
    );
  }
  async getUsersNeverLogin(params?: MetadataParams) {
    return this.http.get<UserNeverLoginResponse>(
      `/never-logged-in-users`,
      params,
    );
  }
}

export const userManagement = new UserManagementApi();
export * from "./types";
