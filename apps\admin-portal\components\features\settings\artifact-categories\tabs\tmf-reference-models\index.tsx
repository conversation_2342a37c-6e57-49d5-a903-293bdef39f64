"use client";
import { Card } from "flowbite-react";
import React, { useEffect, useMemo, useState } from "react";
import { HiPlus } from "react-icons/hi";

import { SearchField } from "@/components/shared";
import LoadingWrapper from "@/components/shared/loading-wrapper";
import { But<PERSON> } from "@/components/ui/button";
import { TableDataPagination } from "@/components/ui/pagination";
import { TableLoading } from "@/components/ui/table";
import { TableData } from "@/components/ui/table/table";
import { useBreadcrumbs } from "@/hooks/use-breadcrumb";
import { useDisclosure } from "@/hooks/use-disclosure";
import { TMFRefModel } from "@/lib/apis/tmf-ref-models";

import { generateTmfColumns } from "./columns";
import { useDeleteTMFRefModel } from "./hooks/use-tmf-ref-model-mutations";
import { useTMFRefModels } from "./hooks/use-tmf-ref-model-queries";
import { TMFModal } from "./tmf-modal";

export const TMFReferenceModelsTab = () => {
  const { isOpen, close, open } = useDisclosure();

  const [selectedTMFModel, setSelectedTMFModel] = useState<TMFRefModel | null>(
    null,
  );
  const { data, isPending, isPlaceholderData } = useTMFRefModels();
  const { mutate, isPending: isDeleting } = useDeleteTMFRefModel();

  const handleCloseModal = () => {
    close();
    setSelectedTMFModel(null);
  };

  const columns = useMemo(
    () =>
      generateTmfColumns({
        onEdit: (data) => {
          setSelectedTMFModel(data);
          open();
        },
        onDelete: (data) => {
          mutate(data.id);
        },
      }),
    [mutate, open],
  );
  return (
    <>
      <h1 className="mb-4 text-2xl font-semibold text-gray-900 sm:text-2xl dark:text-white">
        TMF Reference Models
      </h1>
      <Card className="[&>div]:p-0">
        <div className="flex items-center justify-between gap-4 p-4">
          <div>
            <SearchField placeholder="Search..." />
          </div>
          <Button
            onClick={open}
            variant="primary"
            className="w-fit flex-shrink-0 px-0 py-1.5"
          >
            <HiPlus />
            Add New TMF
          </Button>
        </div>
        {isPending ? (
          <TableLoading columns={columns} />
        ) : (
          <>
            <LoadingWrapper isLoading={isPlaceholderData || isDeleting}>
              <TableData data={data?.results ?? []} columns={columns} />
            </LoadingWrapper>
            {data?.metadata && <TableDataPagination metadata={data.metadata} />}
          </>
        )}
      </Card>
      <TMFModal
        isOpen={isOpen}
        onClose={handleCloseModal}
        selectedTMFModel={selectedTMFModel}
      />
    </>
  );
};
