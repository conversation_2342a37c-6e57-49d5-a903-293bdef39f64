"use client";

import { useParams } from "next/navigation";
import { useState } from "react";
import { RiEditLine } from "react-icons/ri";

import { BackButton } from "@/components/shared";
import { OverviewCard, OverviewItem } from "@/components/shared/overview-card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { formatDate } from "@/lib/utils";

import { useAssignment } from "../hooks/use-assignment";
import { AssignmentTabs } from "./assignment-tabs";
import { ModalEditAssignment } from "./modal-edit-assignment";

export const AssignmentDetailPageContent = () => {
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const params = useParams();
  const id = params.id as string;
  const { data: assignment, isLoading } = useAssignment(id);

  return (
    <>
      <div className="flex flex-col gap-4 ">
        {isLoading ? (
          <AssignmentDetailSkeleton />
        ) : (
          <>
            <div className="flex justify-between">
              <div className="flex items-center gap-4">
                <BackButton href="/assignments" type="link" />
                <div className="leading-7.5 text-xl font-semibold dark:text-white">
                  {assignment?.name}
                </div>
              </div>

              <Button
                variant="primary"
                onClick={() => setIsEditModalOpen(true)}
              >
                <RiEditLine />
                Edit Assignment
              </Button>
            </div>

            <div>
              <OverviewCard title="Overview">
                <div className="grid grid-cols-2 gap-4">
                  <OverviewItem label="Name" value={assignment?.name ?? ""} />
                  <OverviewItem label="Type" value={assignment?.type ?? ""} />
                  <OverviewItem
                    label="Group"
                    value={assignment?.group?.name ?? "N/A"}
                  />
                  <OverviewItem
                    label="Created Date"
                    value={
                      assignment?.createdDate
                        ? formatDate(assignment?.createdDate, "MMM dd, yyyy")
                        : ""
                    }
                  />
                </div>
              </OverviewCard>
            </div>
          </>
        )}

        <AssignmentTabs />
      </div>

      {/* TODO: Add ModalEditAssignment component */}
      {isEditModalOpen && (
        <ModalEditAssignment
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          assignment={assignment}
        />
      )}
    </>
  );
};

const AssignmentDetailSkeleton = () => {
  return (
    <>
      <div className="flex justify-between">
        <div className="flex items-center gap-4">
          <Skeleton className="h-9 w-9 rounded-full" />
          <Skeleton className="h-7 w-48" />
        </div>
        <Skeleton className="h-9 w-36" />
      </div>

      <div>
        <OverviewCard title="Overview">
          <div className="grid grid-cols-2 gap-4">
            <div className="flex flex-col gap-1">
              <Skeleton className="h-5 w-24" />
              <Skeleton className="h-5 w-48" />
            </div>
            <div className="flex flex-col gap-1">
              <Skeleton className="h-5 w-24" />
              <Skeleton className="h-5 w-48" />
            </div>
            <div className="flex flex-col gap-1">
              <Skeleton className="h-5 w-24" />
              <Skeleton className="h-5 w-48" />
            </div>
            <div className="flex flex-col gap-1">
              <Skeleton className="h-5 w-24" />
              <Skeleton className="h-5 w-48" />
            </div>
          </div>
        </OverviewCard>
      </div>
    </>
  );
};

export default AssignmentDetailPageContent;
