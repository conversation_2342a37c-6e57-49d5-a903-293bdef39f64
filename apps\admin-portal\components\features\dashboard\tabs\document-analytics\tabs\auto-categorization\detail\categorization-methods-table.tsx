"use client";

import { Card } from "flowbite-react";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";

import { categorizationMethodColumns } from "./columns";
import { useCategorizationMethods } from "./use-categorization-methods-queries";

export const CategorizationMethodsTable = () => {
  const {
    data: methodsData,
    isPending: isLoading,
    isPlaceholderData,
  } = useCategorizationMethods();

  return (
    <Card className="[&>div]:p-0">
      <h4 className="p-4 text-base font-medium text-gray-900 dark:text-white">
        Categorization Methods
      </h4>

      {isLoading ? (
        <TableLoading columns={categorizationMethodColumns} />
      ) : (
        <LoadingWrapper isLoading={isPlaceholderData}>
          <Table
            columns={categorizationMethodColumns}
            data={methodsData?.results || []}
          />

          {methodsData?.metadata && (
            <TableDataPagination metadata={methodsData.metadata} />
          )}
        </LoadingWrapper>
      )}
    </Card>
  );
};
