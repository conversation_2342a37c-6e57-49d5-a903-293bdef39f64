import { useQuery } from "@tanstack/react-query";

import api from "@/lib/apis";
import { MetadataParams } from "@/lib/apis/types";

const useDataIntegrityKeys = {
  all: () => ["data-integrity"] as const,

  orphanedRecordStatistic: () =>
    [...useDataIntegrityKeys.all(), "orphaned-record-statistic"] as const,

  allNoTMFISFStudiesList: () =>
    [...useDataIntegrityKeys.all(), "no-tmf-isf-studies"] as const,
  noTMFISFStudiesList: (params?: MetadataParams) =>
    [...useDataIntegrityKeys.allNoTMFISFStudiesList(), params] as const,

  allNoCategoryArtifactsList: () =>
    [...useDataIntegrityKeys.all(), "no-category-artifacts"] as const,
  noCategoryArtifactsList: (params?: MetadataParams) =>
    [...useDataIntegrityKeys.allNoCategoryArtifactsList(), params] as const,
};

export const TAKE_50 = 50;

export const useOrphanedRecordStatistic = () => {
  return useQuery({
    queryKey: useDataIntegrityKeys.orphanedRecordStatistic(),
    queryFn: () => api.dataIntegrity.getOrphanedRecordStatistic(),
  });
};

export const useNoTMFISFStudies = (page: number) => {
  const params = {
    page,
    take: TAKE_50,
  };
  return useQuery({
    queryKey: useDataIntegrityKeys.noTMFISFStudiesList(params),
    queryFn: () => api.dataIntegrity.getNoTMFISFStudies(params),
    placeholderData: (prev) => prev,
  });
};

export const useNoCategoryArtifacts = (page: number) => {
  const params = {
    page,
    take: TAKE_50,
  };
  return useQuery({
    queryKey: useDataIntegrityKeys.noCategoryArtifactsList(params),
    queryFn: () => api.dataIntegrity.getNoCategoryArtifacts(params),
    placeholderData: (prev) => prev,
  });
};
