import { useQuery } from "@tanstack/react-query";

import { usePagination } from "@/hooks/use-pagination";
import api from "@/lib/apis";
import { MetadataParams } from "@/lib/apis/types";

const useTasksInactiveKeys = {
  all: () => ["data-integrity", "tasks-inactive"] as const,
  list: (params?: MetadataParams) =>
    [...useTasksInactiveKeys.all(), params] as const,
};

export const TAKE_50 = 50;

export const useTasksInactive = () => {
  const { page } = usePagination();
  const params = {
    page,
    take: TAKE_50,
  };
  return useQuery({
    queryKey: useTasksInactiveKeys.list(params),
    queryFn: () => api.dataIntegrity.getTasksInactive(params),
    placeholderData: (prev) => prev,
  });
};
