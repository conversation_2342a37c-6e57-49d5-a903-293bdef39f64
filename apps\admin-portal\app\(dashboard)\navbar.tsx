"use client";

import { UserButton } from "@clerk/nextjs";
import { DarkThemeToggle, Navbar, Toolt<PERSON> } from "flowbite-react";
import Image from "next/image";
import Link from "next/link";
import { HiMenuAlt1, HiX } from "react-icons/hi";

import { useSidebarContext } from "@/contexts/sidebar-context";
import { useMediaQuery } from "@/hooks/use-media-query";

export function DashboardNavbar() {
  const sidebar = useSidebarContext();
  const isDesktop = useMediaQuery("(min-width: 1024px)");

  function handleToggleSidebar() {
    if (isDesktop) {
      return;
    }
    sidebar.mobile.toggle();
  }
  return (
    <Navbar
      fluid
      className="fixed top-0 z-30 w-full border-b border-gray-700 bg-[#212a36] p-0 text-white sm:p-0 dark:bg-gray-800"
    >
      <div className="w-full p-3 pr-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <button
              onClick={handleToggleSidebar}
              className="mr-3 cursor-pointer rounded p-2 text-gray-600 hover:bg-gray-700 hover:text-gray-300 lg:hidden"
            >
              <span className="sr-only">Toggle sidebar</span>
              {/* mobile */}
              <div>
                {sidebar.mobile.isOpen ? (
                  <HiX className="h-6 w-6" />
                ) : (
                  <HiMenuAlt1 className="h-6 w-6" />
                )}
              </div>
            </button>
            <Navbar.Brand as={Link} href="/" className="mr-14">
              <Image
                className="ml-2 mr-3 h-8"
                alt=""
                src="/images/logo.svg"
                width={152}
                height={27}
              />
            </Navbar.Brand>
          </div>
          <div className="hidden lg:block"></div>
          <div className="flex items-center lg:gap-3">
            <div className="flex items-center">
              <div className="hidden dark:block">
                <Tooltip content="Toggle light mode">
                  <DarkThemeToggle className="hover:bg-gray-700" />
                </Tooltip>
              </div>
              <div className="dark:hidden">
                <Tooltip content="Toggle dark mode">
                  <DarkThemeToggle className="hover:bg-gray-700" />
                </Tooltip>
              </div>
              <div className="ml-3 flex items-center">
                <UserButton />
              </div>
            </div>
          </div>
        </div>
      </div>
    </Navbar>
  );
}
