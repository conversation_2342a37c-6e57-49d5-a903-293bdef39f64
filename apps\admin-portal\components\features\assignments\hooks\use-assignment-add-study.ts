import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import type { AddStudyToAssignmentPayload } from "@/lib/apis/assignments";

import { USE_ASSIGNMENT_STUDIES_QUERY_KEY } from "./use-assignment-studies";

export const useAssignmentAddStudy = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (
      payload: AddStudyToAssignmentPayload & { assignmentId: string },
    ) => api.assignments.addStudy(payload.assignmentId, payload),
    onSuccess: (_, variables) => {
      toast.success("Study added to assignment");
      queryClient.invalidateQueries({
        queryKey: [USE_ASSIGNMENT_STUDIES_QUERY_KEY, variables.assignmentId],
      });
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
};
