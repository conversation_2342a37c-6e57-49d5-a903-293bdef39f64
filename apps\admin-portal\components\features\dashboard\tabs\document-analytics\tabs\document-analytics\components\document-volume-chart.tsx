"use client";

import { Card } from "flowbite-react";
import {
  Area,
  AreaChart,
  Legend,
  ResponsiveContainer,
  Tooltip,
  TooltipContentProps,
  XAxis,
  YAxis,
} from "recharts";
import {
  NameType,
  ValueType,
} from "recharts/types/component/DefaultTooltipContent";

import { Skeleton } from "@/components/ui/skeleton";
import { formatDate } from "@/lib/utils";

import type { DocumentVolumeOverTimeResponse } from "../../../types";
import { useDocumentVolumeOverTime } from "../hooks/use-document-analytics-queries";

type ChartDataPoint = {
  date: string;
  formattedDate: string;
  source: number;
  artifact: number;
  docExchange: number;
};
const transformData = (
  volumeData?: DocumentVolumeOverTimeResponse,
): ChartDataPoint[] => {
  if (
    !volumeData?.artifact.length &&
    !volumeData?.source.length &&
    !volumeData?.docExchange
  )
    return [];
  const data = [
    ...volumeData.source,
    ...volumeData.artifact,
    ...volumeData.docExchange,
  ];
  const dateMap = new Map<string, ChartDataPoint>();

  data.forEach((item) => {
    const date = new Date(item.date).toISOString().split("T")[0];
    const formattedDate = formatDate(item.date, "LLL dd");

    dateMap.set(date, {
      date,
      formattedDate,
      source:
        volumeData.source.find((source) => source.date === item.date)?.count ||
        0,
      artifact:
        volumeData.artifact.find((artifact) => artifact.date === item.date)
          ?.count || 0,
      docExchange:
        volumeData.docExchange.find(
          (docExchange) => docExchange.date === item.date,
        )?.count || 0,
    });
  });

  return Array.from(dateMap.values()).sort((a, b) =>
    a.date.localeCompare(b.date),
  );
};
const CustomTooltip = ({
  active,
  payload,
  label,
}: TooltipContentProps<ValueType, NameType>) => {
  if (active && payload && payload.length) {
    return (
      <div className="rounded-lg border bg-white p-3 shadow-lg dark:border-gray-600 dark:bg-gray-800">
        <p className="font-medium text-gray-900 dark:text-white">{label}</p>
        {payload.map((entry, index: number) => (
          <p key={index} className="text-sm" style={{ color: entry.color }}>
            {`${entry.name}: ${entry.value}`}
          </p>
        ))}
      </div>
    );
  }
  return null;
};

export const DocumentVolumeChart = () => {
  const { data: volumeData, isPending } = useDocumentVolumeOverTime();

  const chartData = transformData(volumeData);

  if (isPending) {
    return (
      <Card>
        <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
          Document Volume Over Time
        </h3>
        <Skeleton className="h-80 w-full" />
      </Card>
    );
  }

  return (
    <Card>
      <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
        Document Volume Over Time
      </h3>

      <div className="h-80">
        {chartData.length === 0 ? (
          <div className="flex h-full items-center justify-center">
            <p className="text-xl text-gray-500 dark:text-gray-400">
              No data available
            </p>
          </div>
        ) : (
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={chartData}>
              <defs>
                <linearGradient id="colorSource" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.8} />
                  <stop offset="95%" stopColor="#3B82F6" stopOpacity={0.1} />
                </linearGradient>
                <linearGradient id="colorArtifact" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#10B981" stopOpacity={0.8} />
                  <stop offset="95%" stopColor="#10B981" stopOpacity={0.1} />
                </linearGradient>
                <linearGradient
                  id="colorDocExchange"
                  x1="0"
                  y1="0"
                  x2="0"
                  y2="1"
                >
                  <stop offset="5%" stopColor="#F59E0B" stopOpacity={0.8} />
                  <stop offset="95%" stopColor="#F59E0B" stopOpacity={0.1} />
                </linearGradient>
              </defs>
              <XAxis
                interval="preserveStartEnd"
                dataKey="formattedDate"
                tick={{ fontSize: 12, fill: "#6B7280" }}
              />
              <YAxis tick={{ fontSize: 12, fill: "#6B7280" }} />
              <Tooltip content={CustomTooltip} />
              <Legend />
              <Area
                type="monotone"
                dataKey="source"
                stackId="1"
                stroke="#3B82F6"
                fill="url(#colorSource)"
                name="Source"
              />
              <Area
                type="monotone"
                dataKey="artifact"
                stackId="1"
                stroke="#10B981"
                fill="url(#colorArtifact)"
                name="Artifact"
              />
              <Area
                type="monotone"
                dataKey="docExchange"
                stackId="1"
                stroke="#F59E0B"
                fill="url(#colorDocExchange)"
                name="Doc Exchange"
              />
            </AreaChart>
          </ResponsiveContainer>
        )}
      </div>
    </Card>
  );
};
