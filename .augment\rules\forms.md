---
type: "agent_requested"
description: "Example description"
---

# Form Implementation Standards

This document establishes standardized patterns for form implementation across the admin-portal application, based on the training course modal as the primary reference implementation.

## Core Libraries

- **React Hook Form** - Used for form state management and validation
- **Zod** - Used for schema validation and TypeScript type inference
- **Flowbite React** - Base UI components for form elements
- **@hookform/resolvers/zod** - Integration between React Hook Form and Zod

## Form Architecture Standards

### Basic Form Structure

All forms should follow this standardized structure using the `Form` wrapper component:

```typescript
// components/features/training/tabs/courses/course-modal.tsx
import { z } from "zod";
import { Form, InputField, Select, Textarea } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";

// 1. Define Zod schema
const schema = z.object({
  name: z
    .string({
      required_error: "Name is required",
      invalid_type_error: "Name is required",
    })
    .min(1, "Name is required"),
  status: z.enum(COURSE_STATUSES, {
    errorMap: () => ({
      message: "Status is required",
    }),
  }),
  order: z.coerce
    .number({
      invalid_type_error: "Order must be a number",
    })
    .optional(),
  description: z.string().optional(),
});

// 2. Form component implementation
export const CourseModal = ({ isOpen, onClose, selectedCourse }: Props) => {
  const { mutateAsync: addCourse, isPending: isAdding } = useAddCourse();
  const { mutateAsync: updateCourse, isPending: isUpdating } = useUpdateCourse();

  const isEditing = !!selectedCourse;

  const onSubmit = async (data: z.infer<typeof schema>) => {
    isEditing ? await updateCourse(data) : await addCourse(data);
    onClose();
  };

  return (
    <WrapperModal isOpen={isOpen} onClose={onClose} title={`${isEditing ? "Edit" : "Add"} Course`}>
      <Form
        defaultValues={{
          name: selectedCourse?.name || "",
          description: selectedCourse?.description || "",
          status: selectedCourse?.status || "",
          order: selectedCourse?.order,
        }}
        mode="onChange"
        schema={schema}
        onSubmit={onSubmit}
      >
        {/* Form fields */}
        <div className="grid gap-4 sm:grid-cols-2 sm:gap-6">
          <div className="space-y-2">
            <Label htmlFor="name">Name</Label>
            <InputField id="name" name="name" placeholder="Enter course name..." />
          </div>
          {/* More fields... */}
        </div>

        {/* Form actions */}
        <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
          <CloseButton onClose={onClose} />
          <Button
            type="submit"
            disabled={isAdding || isUpdating}
            isLoading={isAdding || isUpdating}
            color="blue"
          >
            Save
          </Button>
        </div>
      </Form>
    </WrapperModal>
  );
};
```

### Form Component Props

The `Form` component accepts these key props:

```typescript
export type FormProps<
  TSchema extends z.ZodTypeAny,
  TDefaultValues extends z.infer<TSchema>,
> = {
  schema?: TSchema; // Zod validation schema
  onSubmit: (data: z.infer<TSchema>, formHandler?: UseFormReturn) => void;
  defaultValues?: DefaultValues<TDefaultValues>; // Initial form values
  mode?: Mode; // Validation mode: "onChange" | "onBlur" | "onSubmit"
  isSubmitting?: boolean; // External loading state
  formProps?: Pick<UseFormProps, "shouldFocusError">;
  formMethods?: UseFormReturn; // External form methods
  className?: string;
};
```

## Zod Schema Validation Patterns

### Standard Validation Patterns

```typescript
// Required string fields
name: z
  .string({
    required_error: "Name is required",
    invalid_type_error: "Name is required",
  })
  .min(1, "Name is required"),

// Email validation
email: z
  .string({ required_error: "Email is required" })
  .email("Invalid email format")
  .min(1, "Email is required"),

// Enum validation
status: z.enum(COURSE_STATUSES, {
  errorMap: () => ({
    message: "Status is required",
  }),
}),

// Number coercion with validation
order: z.coerce
  .number({
    invalid_type_error: "Order must be a number",
  })
  .optional(),

// Optional fields
description: z.string().optional(),
phone: z.string().nullish(),

// UUID validation
moduleId: z
  .string({ required_error: "Module is required" })
  .uuid("Please select a valid Module ID"),

// Custom validation with regex
rebootFrequency: z.coerce
  .string({ required_error: "Reboot frequency is required" })
  .regex(/^\d+$/, "Reboot frequency must be greater or equal to 0")
  .transform((val) => parseInt(val, 10))
  .pipe(z.number().min(0, "Reboot frequency must be greater or equal to 0")),

// Conditional validation with refine
.refine((data) => !(data.type === "site" && !data.siteId), {
  message: "siteId is required when type is 'site'",
  path: ["siteId"],
});
```

### Nested Schema Composition

```typescript
export const addressSchema = z.object({
  addressLine: z
    .string({ required_error: "Address line is required" })
    .min(1, "Address line is required"),
  city: z
    .string({ required_error: "City is required" })
    .min(1, "City is required"),
  countryId: z
    .string({ required_error: "Country is required" })
    .min(1, "Country is required"),
});
```

### Constants and Type Safety

```typescript
// Define constants for type safety
export const COURSE_STATUSES = ["draft", "published", "archived"] as const;
export const GROUP_TYPES = ["site", "cro", "clincove"] as const;

// Use in schema
const schema = z.object({
  status: z.enum(COURSE_STATUSES),
  type: z.enum(GROUP_TYPES),
});

// TypeScript type inference
type CourseStatus = (typeof COURSE_STATUSES)[number]; // "draft" | "published" | "archived"
```

## Reusable Form Components

### InputField Component

Standard text input with automatic error handling:

```typescript
import { InputField } from "@/components/ui/form";

// Basic usage
<InputField
  name="name"
  placeholder="Enter name..."
  id="name"
/>

```

**Props:**

- `name: string` - Required field name for form binding
- `shouldShowError?: boolean` - Show/hide error messages (default: true)
- All standard HTML input props

### Textarea Component

Multi-line text input:

```typescript
import { Textarea } from "@/components/ui/form";

<Textarea
  name="description"
  placeholder="Enter description..."
  rows={3}  // Default: 3
/>
```

### Select Component

Dropdown selection with floating UI:

```typescript
import { Select } from "@/components/ui/form";

<Select
  name="status"
  placeholder="Select status"
  options={[
    { label: "Draft", value: "draft" },
    { label: "Published", value: "published" },
    { label: "Archived", value: "archived", disabled: true },
  ]}
  dependentFieldNames={["relatedField"]} // Clear when dependent field changes
/>
```

**Props:**

- `options: { label: React.ReactNode; value: string; disabled?: boolean }[]`
- `dependentFieldNames?: string[]` - Clear field when these fields change
- `shouldShowError?: boolean`

### LazySelectV2 Component

Async select with infinite loading for large datasets:

```typescript
import { LazySelectV2 } from "@/components/ui/lazy-select/lazy-select-v2";

<LazySelectV2
  name="moduleId"
  searchPlaceholder="Search module..."
  useInfiniteQuery={useInfiniteModules}
  getOptionLabel={(module) => module.name}
  getOptionValue={(module) => module.id}
  placeholder="Select module"
  params={[]} // Additional params for query
/>
```

**Props:**

- `useInfiniteQuery: (search: string, ...args: any[]) => UseInfiniteQueryResult`
- `getOptionLabel: (option: T) => string`
- `getOptionValue: (option: T) => string`
- `mapData?: (data: T[]) => any[]` - Transform fetched data
- `params?: any[]` - Additional parameters for the query
- `dependentFieldNames?: string[]` - Clear field when these fields change

### InputNumber Component

Numeric input with increment/decrement buttons:

```typescript
import { InputNumber } from "@/components/ui/form/input-number-with-buttons";

<InputNumber
  name="order"
  placeholder="Enter order..."
  isShowButtons={true}        // Show +/- buttons
  isAllowNegative={false}     // Allow negative numbers
  isAllowDecimalNumber={true} // Allow decimal numbers
  min={0}
  max={100}
  step={1}
/>
```

### Checkbox Component

Boolean input with automatic checked state management:

```typescript
import { Checkbox } from "@/components/ui/form";

<Checkbox
  name="isActive"
  id="isActive"
/>
```

### Radio Component

Radio button groups for single selection:

```typescript
import { Radio } from "@/components/ui/form";

{/* Radio group */}
<div className="flex gap-4">
  <div className="flex items-center gap-2">
    <Radio name="type" value="site" id="type-site" />
    <Label htmlFor="type-site">Site</Label>
  </div>
  <div className="flex items-center gap-2">
    <Radio name="type" value="cro" id="type-cro" />
    <Label htmlFor="type-cro">CRO</Label>
  </div>
</div>
```

### MultiSelect Component

Multiple selection dropdown:

```typescript
import { MultiSelect } from "@/components/ui/form";

<MultiSelect
  name="tags"
  placeholder="Select tags"
  options={[
    { label: "Tag 1", value: "tag1" },
    { label: "Tag 2", value: "tag2" },
  ]}
  onChange={(values) => console.log(values)} // string[]
/>
```

### FileDropzone Component

File upload with drag-and-drop support:

```typescript
import { FileDropzone } from "@/components/ui/file-drop-zone";

<FileDropzone
  name="files"
  multiple={true}                    // Allow multiple files
  acceptTypes={[".pdf", ".docx"]}    // Allowed file types
  maxSizeMB={50}                     // Max file size in MB
  shouldValidate={true}              // Trigger validation on change
/>
```

### Label Component

Form labels with automatic required field indicators:

```typescript
import { Label } from "@/components/ui/form/label";

<Label htmlFor="name">Name</Label>  {/* Shows * if field is required in schema */}
<Label htmlFor="email" required>Email</Label>  {/* Force required indicator */}
```

**Features:**

- Automatically shows `*` for required fields based on Zod schema
- Can manually force required indicator with `required` prop
- Integrates with `useRequiredFields` context

## Form Layout Patterns

### Grid Layout

Standard responsive grid layout for forms:

```typescript
{/* 2-column grid on desktop, 1-column on mobile */}
<div className="grid gap-4 sm:grid-cols-2 sm:gap-6">
  <div className="space-y-2">
    <Label htmlFor="firstName">First Name</Label>
    <InputField name="firstName" id="firstName" />
  </div>
  <div className="space-y-2">
    <Label htmlFor="lastName">Last Name</Label>
    <InputField name="lastName" id="lastName" />
  </div>

  {/* Full width field */}
  <div className="space-y-2 sm:col-span-2">
    <Label htmlFor="description">Description</Label>
    <Textarea name="description" id="description" />
  </div>
</div>
```

### Form Actions Layout

Standard button layout for form actions:

```typescript
<div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
  <CloseButton onClose={onClose} />
  <Button
    type="submit"
    disabled={isSubmitting}
    isLoading={isSubmitting}
    color="blue"
  >
    Save
  </Button>
</div>
```

## Form Submission Patterns

### Standard Submission with Loading States

```typescript
export const MyModal = ({ isOpen, onClose, selectedItem }: Props) => {
  const { mutateAsync: createItem, isPending: isCreating } = useCreateItem();
  const { mutateAsync: updateItem, isPending: isUpdating } = useUpdateItem();

  const isEditing = !!selectedItem;
  const isSubmitting = isCreating || isUpdating;

  const onSubmit = async (data: z.infer<typeof schema>) => {
    try {
      if (isEditing) {
        await updateItem({ ...data, id: selectedItem.id });
      } else {
        await createItem(data);
      }
      onClose();
    } catch (error) {
      // Error handling is typically done in the mutation hooks
      console.error('Submission failed:', error);
    }
  };

  return (
    <Form
      defaultValues={{
        name: selectedItem?.name || "",
        // ... other default values
      }}
      schema={schema}
      onSubmit={onSubmit}
      isSubmitting={isSubmitting} // Optional: for additional loading state
    >
      {/* Form content */}
      <Button
        type="submit"
        disabled={isSubmitting}
        isLoading={isSubmitting}
      >
        {isEditing ? "Update" : "Create"}
      </Button>
    </Form>
  );
};
```

### Form Reset and Default Values

```typescript
// Reset form to default values
const formRef = useRef<FormRef<typeof schema>>(null);

const handleReset = () => {
  formRef.current?.formHandler.reset();
};

// Dynamic default values
const defaultValues = useMemo(() => ({
  name: selectedItem?.name || "",
  status: selectedItem?.status || "draft",
  // Ensure consistent structure
}), [selectedItem]);

<Form
  ref={formRef}
  defaultValues={defaultValues}
  schema={schema}
  onSubmit={onSubmit}
>
```

## Naming Conventions

### Schema and Component Naming

```typescript
// Schema naming: {entity}Schema
export const courseSchema = z.object({...});
export const userSchema = z.object({...});
export const addAssignmentSchema = z.object({...}); // For specific actions

// Component naming: {Entity}Modal, {Entity}Form
export const CourseModal = ({ ... }) => { ... };

// Constants naming: SCREAMING_SNAKE_CASE
export const COURSE_STATUSES = ["draft", "published"] as const;
export const GROUP_TYPES = ["site", "cro", "clincove"] as const;
```

### Field Naming

```typescript
// Use camelCase for field names
const schema = z.object({
  firstName: z.string(),
  lastName: z.string(),
  emailAddress: z.string().email(),
  phoneNumber: z.string().optional(),
  isActive: z.boolean(),
  createdAt: z.date(),
});

// Match API field names when possible
const schema = z.object({
  displayName: z.string(), // Matches API field
  currentVersionId: z.string().uuid(),
  targetVersionId: z.string().uuid(),
});
```

## Best Practices Summary

### Form Architecture

1. **Always use Zod schemas** - Define validation schemas for type safety and validation
2. **Use the Form wrapper** - Leverage the standardized Form component for consistency
3. **Implement proper loading states** - Show loading indicators during form submission
4. **Handle both create and edit modes** - Use conditional logic for add/edit functionality

### Component Usage

1. **Use appropriate form components** - Choose the right component for each input type
2. **Implement consistent layouts** - Follow established grid and spacing patterns
3. **Provide proper accessibility** - Use labels, IDs, and ARIA attributes correctly
4. **Handle dependent fields** - Clear dependent fields when parent fields change

### Performance and UX

1. **Optimize re-renders** - Use proper memoization for default values and callbacks
2. **Implement proper focus management** - Guide users through form completion
3. **Provide immediate feedback** - Show validation errors and success states promptly
4. **Support keyboard navigation** - Ensure forms are fully keyboard accessible

### Code Organization

1. **Co-locate schemas with components** - Keep validation schemas near their usage
2. **Extract reusable schemas** - Create shared schemas for common patterns (e.g., address)
3. **Use consistent naming** - Follow established naming conventions for schemas and components
4. **Document complex validations** - Add comments for complex validation logic

This documentation serves as the definitive guide for implementing consistent, accessible, and maintainable forms across the admin-portal application.
